# LINE 贴图爬虫多线程优化指南

## 概述

原有的 `getCategoryPath()` 方法采用单线程顺序爬取，效率较低。现已改造为多线程版本，可以显著提升爬取速度。

## 主要改进

### 1. 多层级并行处理
- **分类级别并行**: 多个分类同时爬取
- **贴图级别并行**: 每个分类内的贴图详情并行获取
- **两级线程池**: 分别控制分类和详情的并发数

### 2. 线程安全优化
- 使用 `synchronized` 关键字保护数据库操作
- 使用 `CountDownLatch` 协调线程同步
- 使用 `AtomicInteger` 进行线程安全的计数

### 3. 错误处理和重试机制
- 支持自动重试失败的请求
- 详细的错误日志记录
- 优雅的线程池关闭

## 使用方法

### 方法1: 使用改造后的原方法
```java
@Test
public void getCategoryPath() {
    // 现在默认使用多线程版本
}
```

### 方法2: 显式调用多线程方法
```java
@Test  
public void testMultiThread() {
    getCategoryPathMultiThread();
}
```

### 方法3: 使用单线程版本（备选）
```java
@Test
public void testSingleThread() {
    getCategoryPathSingleThread();
}
```

## 性能配置

### 线程池大小配置
```java
// 分类爬取线程池大小 - 建议根据分类数量调整
private static final int CATEGORY_THREAD_POOL_SIZE = 4;

// 详情爬取线程池大小 - 建议根据服务器性能调整  
private static final int DETAIL_THREAD_POOL_SIZE = 16;
```

### 推荐配置

| 服务器配置 | 分类线程池 | 详情线程池 | 说明 |
|-----------|-----------|-----------|------|
| 2核4G | 2 | 8 | 适合小型服务器 |
| 4核8G | 4 | 16 | 推荐配置 |
| 8核16G | 6 | 32 | 高性能服务器 |

### 注意事项

1. **避免过多线程**: 线程数过多可能导致：
   - 频繁的上下文切换
   - 内存占用过高
   - 目标网站反爬虫机制触发

2. **数据库连接池**: 确保数据库连接池大小足够支持并发操作

3. **网络限制**: 考虑目标网站的访问频率限制

## 性能对比

### 单线程版本
- 顺序处理每个分类
- 每个分类内顺序处理每个贴图
- 预计耗时: 数小时

### 多线程版本  
- 4个分类并行处理
- 每个分类内16个贴图并行处理
- 预计耗时: 减少70-80%

## 监控和调试

### 进度监控
```java
// 分类级别进度
log.info("分类 {} 处理完成，进度: {}/{}", category.getTitle(), processed, categories.size());

// 贴图级别进度  
log.info("分类 {} 贴图处理进度: {}/{}", category.getTitle(), processed, stickerInfos.size());
```

### 性能统计
```java
long startTime = System.currentTimeMillis();
getCategoryPathMultiThread();
long endTime = System.currentTimeMillis();
log.info("总耗时: {} 秒", (endTime - startTime) / 1000.0);
```

## 故障排除

### 常见问题

1. **内存不足**
   - 减少线程池大小
   - 增加JVM堆内存: `-Xmx4g`

2. **数据库连接不足**
   - 增加数据库连接池大小
   - 减少详情线程池大小

3. **网络超时**
   - 增加HTTP超时时间
   - 添加重试机制

4. **目标网站限制**
   - 减少并发数
   - 添加请求间隔

### 调试模式
```java
// 启用详细日志
logging.level.com.apk.website=DEBUG

// 监控线程池状态
ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newFixedThreadPool(16);
log.info("活跃线程: {}, 队列大小: {}", executor.getActiveCount(), executor.getQueue().size());
```

## 最佳实践

1. **逐步增加并发数**: 从小的线程池开始，逐步增加到最优值
2. **监控系统资源**: 观察CPU、内存、网络使用情况
3. **设置合理超时**: 避免线程长时间阻塞
4. **优雅关闭**: 确保线程池正确关闭，避免资源泄露
5. **错误恢复**: 实现断点续传，支持从失败处继续

## 扩展功能

### 1. 动态调整线程数
```java
// 根据系统负载动态调整
int coreCount = Runtime.getRuntime().availableProcessors();
int threadCount = Math.min(coreCount * 2, 32);
```

### 2. 限流控制
```java
// 使用信号量控制并发数
Semaphore semaphore = new Semaphore(10);
semaphore.acquire();
try {
    // 执行爬取操作
} finally {
    semaphore.release();
}
```

### 3. 断点续传
```java
// 记录已处理的分类，支持中断后继续
Set<String> processedCategories = loadProcessedCategories();
if (!processedCategories.contains(category.getTitle())) {
    // 处理分类
    processedCategories.add(category.getTitle());
    saveProcessedCategories(processedCategories);
}
```

通过以上优化，爬取效率可以提升数倍，同时保持系统稳定性和数据完整性。
