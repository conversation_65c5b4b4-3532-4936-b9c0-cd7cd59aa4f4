# LINE 贴图爬虫多线程版本使用说明

## 快速开始

你的 `getCategoryPath()` 方法已经成功改造为多线程版本！现在可以大幅提升爬取速度。

### 直接运行（推荐）
```java
@Test
public void getCategoryPath() {
    // 现在默认使用多线程版本，无需修改调用方式
}
```

### 性能对比

| 版本 | 处理方式 | 预计耗时 | 资源占用 |
|------|----------|----------|----------|
| 原版本 | 单线程顺序处理 | 数小时 | CPU利用率低 |
| 多线程版本 | 4个分类 + 16个详情并行 | 减少70-80% | CPU利用率高 |

## 主要改进

### 1. 两级并行处理
- **分类级别**: 4个分类同时爬取
- **详情级别**: 每个分类内16个贴图详情并行获取

### 2. 智能进度监控
```
[INFO] 开始多线程爬取 12 个分类
[INFO] 线程 pool-1-thread-1 开始处理分类: 动物与宠物
[INFO] 线程 pool-1-thread-2 开始处理分类: 卡通人物
[INFO] 分类 动物与宠物 开始多线程爬取 156 个贴图详情
[INFO] 分类 动物与宠物 贴图处理进度: 10/156 (成功: 10, 失败: 0)
```

### 3. 错误处理和重试
- 自动重试失败的请求（最多3次）
- 详细的错误日志
- 优雅的线程池关闭

## 配置调优

### 当前配置
```java
private static final int CATEGORY_THREAD_POOL_SIZE = 4;  // 分类线程池
private static final int DETAIL_THREAD_POOL_SIZE = 16;   // 详情线程池
```

### 根据服务器性能调整

**低配置服务器 (2核4G)**:
```java
private static final int CATEGORY_THREAD_POOL_SIZE = 2;
private static final int DETAIL_THREAD_POOL_SIZE = 8;
```

**高配置服务器 (8核16G)**:
```java
private static final int CATEGORY_THREAD_POOL_SIZE = 6;
private static final int DETAIL_THREAD_POOL_SIZE = 32;
```

## 使用选项

### 选项1: 多线程版本（默认）
```java
@Test
public void getCategoryPath() {
    // 自动使用多线程版本
}
```

### 选项2: 单线程版本（备选）
```java
@Test  
public void testSingleThread() {
    getCategoryPathSingleThread();
}
```

### 选项3: 自定义配置
```java
// 在 LineStore.java 中修改这些常量
private static final int CATEGORY_THREAD_POOL_SIZE = 6;  // 增加分类并发数
private static final int DETAIL_THREAD_POOL_SIZE = 24;   // 增加详情并发数
```

## 监控和调试

### 实时监控
运行时会看到详细的进度信息：
```
[INFO] 开始多线程爬取 12 个分类
[INFO] 分类 动物与宠物 处理完成，进度: 1/12
[INFO] 分类 卡通人物 处理完成，进度: 2/12
[INFO] 所有分类爬取完成！
[INFO] 总耗时: 1234.5 秒
```

### 性能统计
```java
long startTime = System.currentTimeMillis();
getCategoryPath();
long endTime = System.currentTimeMillis();
log.info("爬取完成，总耗时: {} 秒", (endTime - startTime) / 1000.0);
```

## 注意事项

### 1. 数据库连接
确保数据库连接池大小足够：
```properties
# application.properties
spring.datasource.hikari.maximum-pool-size=50
```

### 2. 内存设置
如果遇到内存不足，增加JVM堆内存：
```bash
java -Xmx4g -jar your-app.jar
```

### 3. 网络限制
如果遇到网络限制，可以：
- 减少线程数
- 增加请求间隔
- 使用代理池

## 故障排除

### 问题1: 内存不足
**症状**: OutOfMemoryError
**解决**: 
- 减少 `DETAIL_THREAD_POOL_SIZE` 到 8
- 增加 JVM 内存: `-Xmx4g`

### 问题2: 数据库连接不足  
**症状**: Connection pool exhausted
**解决**:
- 增加数据库连接池大小
- 减少并发线程数

### 问题3: 网络超时
**症状**: SocketTimeoutException
**解决**:
- 检查网络连接
- 减少并发数避免触发反爬虫

### 问题4: 爬取速度仍然慢
**可能原因**:
- 线程数设置过低
- 网络带宽限制
- 目标网站响应慢

**解决方案**:
- 逐步增加线程数测试最优值
- 监控网络使用情况
- 添加更多错误处理和重试

## 最佳实践

1. **从小开始**: 先用较小的线程数测试，确保稳定后再增加
2. **监控资源**: 观察CPU、内存、网络使用情况
3. **错误处理**: 关注错误日志，及时调整策略
4. **数据验证**: 定期检查爬取的数据完整性

## 扩展功能

如果需要更高级的功能，可以考虑：
- 分布式爬取（多台服务器）
- 智能限流（根据响应时间调整）
- 断点续传（支持中断后继续）
- 数据去重（避免重复爬取）

---

现在你可以直接运行 `getCategoryPath()` 方法，享受多线程带来的速度提升！🚀
