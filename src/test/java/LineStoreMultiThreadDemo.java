
import com.apk.website.Application;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.mapper.TStickerInfoDataMapper;
import com.apk.website.schedule.utils.QiniuUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
@Slf4j
public class LineStoreMultiThreadDemo {
    
    @Autowired
    QiniuUtils qiniuUtils;

    @Autowired
    private TStickerInfoDataMapper tStickerInfoDataMapper;

    // 多线程配置 - 可根据服务器性能调整
    private static final int CATEGORY_THREAD_POOL_SIZE = 4; // 分类爬取线程池大小
    private static final int DETAIL_THREAD_POOL_SIZE = 16; // 详情爬取线程池大小
    private static final int MAX_RETRIES = 3; // 最大重试次数
    
    @Data
    static class Category {
        private String title;
        private int count;
        private String path;
        private int id;
    }
    
    @Data
    static class StickerInfo {
        private String href;
        private String title;
        private Category category;
        
        public StickerInfo(String href, String title, Category category) {
            this.href = href;
            this.title = title;
            this.category = category;
        }
    }

    @Test
    public void demonstrateMultiThreadCrawling() {
        log.info("=== LINE 贴图多线程爬虫演示 ===");
        log.info("分类线程池大小: {}", CATEGORY_THREAD_POOL_SIZE);
        log.info("详情线程池大小: {}", DETAIL_THREAD_POOL_SIZE);
        
        long startTime = System.currentTimeMillis();
        
        // 执行多线程爬取
        getCategoryPathMultiThread();
        
        long endTime = System.currentTimeMillis();
        log.info("=== 爬取完成，总耗时: {} 秒 ===", (endTime - startTime) / 1000.0);
    }
    
    /**
     * 多线程版本的分类爬取
     * 优势：
     * 1. 并行处理多个分类，大幅提升效率
     * 2. 每个分类内部也使用多线程处理贴图详情
     * 3. 支持进度监控和错误处理
     */
    public void getCategoryPathMultiThread() {
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/stickershop/showcase/top/zh-Hant";
        Request request = OkHttpClientUtils.buildRequest(url);
        
        List<Category> categories = new ArrayList<>();
        
        try (Response response = clientInstance.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String body = response.body().string();
                Document parse = Jsoup.parse(body);
                Elements elements = parse.select("ul.mdCMN13Ul").first().select("li");
                elements.remove(0); // 移除全部这一行
                
                // 先收集所有分类信息
                for (Element element : elements) {
                    Category category = new Category();
                    String href = element.select("a").attr("href");
                    String categoryName = element.select("a").text();
                    String count = element.select("em.mdCMN13Count").text().replace("(", "").replace(")", "").replace(",", "");
                    log.info("发现分类: {}，链接：{}，数量：{}", categoryName, href, count);
                    category.setPath(href);
                    category.setCount(Integer.parseInt(count));
                    category.setTitle(categoryName);
                    category.setId(Integer.parseInt(href.replace("?category=", "")));
                    categories.add(category);
                }
                
                // 使用线程池并行处理所有分类
                ExecutorService categoryExecutor = Executors.newFixedThreadPool(CATEGORY_THREAD_POOL_SIZE);
                CountDownLatch categoryLatch = new CountDownLatch(categories.size());
                AtomicInteger processedCategories = new AtomicInteger(0);
                
                log.info("开始多线程爬取 {} 个分类", categories.size());
                
                for (Category category : categories) {
                    categoryExecutor.submit(() -> {
                        try {
                            log.info("线程 {} 开始处理分类: {}", Thread.currentThread().getName(), category.getTitle());
                            getStickerListByCategoryMultiThread(category);
                            int processed = processedCategories.incrementAndGet();
                            log.info("分类 {} 处理完成，进度: {}/{}", category.getTitle(), processed, categories.size());
                        } catch (Exception e) {
                            log.error("处理分类 {} 时发生错误", category.getTitle(), e);
                        } finally {
                            categoryLatch.countDown();
                        }
                    });
                }
                
                try {
                    categoryLatch.await(); // 等待所有分类处理完成
                    log.info("所有分类爬取完成！");
                } catch (InterruptedException e) {
                    log.error("等待分类处理完成时被中断", e);
                    Thread.currentThread().interrupt();
                } finally {
                    categoryExecutor.shutdown();
                    try {
                        if (!categoryExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                            categoryExecutor.shutdownNow();
                        }
                    } catch (InterruptedException e) {
                        categoryExecutor.shutdownNow();
                        Thread.currentThread().interrupt();
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 多线程版本的分类内贴图爬取
     * 特点：
     * 1. 先收集所有需要爬取的贴图链接
     * 2. 使用线程池并行处理贴图详情
     * 3. 支持进度监控
     */
    public void getStickerListByCategoryMultiThread(Category category) {
        log.info("开始多线程爬取分类: {}，预计数量：{}", category.getTitle(), category.getCount());
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/stickershop/showcase/top/zh-Hant";

        int MAX_PAGE = (int) Math.ceil((double) category.getCount() / 36);
        
        // 收集所有需要爬取的贴图信息
        List<StickerInfo> stickerInfos = new ArrayList<>();
        
        for (int pageNum = 1; pageNum <= MAX_PAGE; pageNum++) {
            Request request = OkHttpClientUtils.buildRequest(url + category.getPath() + "&page=" + pageNum);
            try (Response response = clientInstance.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String body = response.body().string();
                    if (body.contains("您要求存取的網頁不存在。")) {
                        log.info("分类 {} 第 {} 页已爬完", category.getTitle(), pageNum);
                        break;
                    }
                    Document parse = Jsoup.parse(body);
                    Elements elements = parse.select("div.MdCMN02List").first().select("ul").first().select("li");
                    for (Element element : elements) {
                        String href = element.select("a").attr("href");
                        String title = element.select("a").select("p").text();
                        String stickerId = href.replaceAll(".*/stickershop/product/([0-9]+)/.*", "$1");
                        if (tStickerInfoDataMapper.selectById(stickerId) != null){
                            log.debug("贴图 {} 已存在，跳过", stickerId);
                            continue;
                        }
                        stickerInfos.add(new StickerInfo(href, title, category));
                    }
                } else {
                    log.warn("分类 {} 第 {} 页爬取失败，状态码: {}", category.getTitle(), pageNum, response.code());
                    break;
                }
            } catch (IOException e) {
                log.error("爬取分类 {} 第 {} 页时发生错误", category.getTitle(), pageNum, e);
                break;
            }
        }
        
        if (stickerInfos.isEmpty()) {
            log.info("分类 {} 没有新的贴图需要爬取", category.getTitle());
            return;
        }
        
        // 使用线程池并行处理贴图详情
        ExecutorService detailExecutor = Executors.newFixedThreadPool(DETAIL_THREAD_POOL_SIZE);
        CountDownLatch detailLatch = new CountDownLatch(stickerInfos.size());
        AtomicInteger processedStickers = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        log.info("分类 {} 开始多线程爬取 {} 个贴图详情", category.getTitle(), stickerInfos.size());
        
        for (StickerInfo stickerInfo : stickerInfos) {
            detailExecutor.submit(() -> {
                try {
                    getStickerDetailWithRetry(stickerInfo.href, stickerInfo.title, stickerInfo.category);
                    successCount.incrementAndGet();
                    int processed = processedStickers.incrementAndGet();
                    if (processed % 10 == 0) { // 每处理10个打印一次进度
                        log.info("分类 {} 贴图处理进度: {}/{} (成功: {}, 失败: {})", 
                                category.getTitle(), processed, stickerInfos.size(), 
                                successCount.get(), failCount.get());
                    }
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    log.error("处理贴图 {} 时发生错误", stickerInfo.title, e);
                } finally {
                    detailLatch.countDown();
                }
            });
        }
        
        try {
            detailLatch.await(); // 等待所有贴图详情处理完成
            log.info("分类 {} 爬取完成！总计: {}, 成功: {}, 失败: {}", 
                    category.getTitle(), stickerInfos.size(), successCount.get(), failCount.get());
        } catch (InterruptedException e) {
            log.error("等待分类 {} 贴图处理完成时被中断", category.getTitle(), e);
            Thread.currentThread().interrupt();
        } finally {
            detailExecutor.shutdown();
            try {
                if (!detailExecutor.awaitTermination(300, TimeUnit.SECONDS)) {
                    detailExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                detailExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 带重试机制的贴图详情获取
     */
    private void getStickerDetailWithRetry(String href, String title, Category category) {
        for (int retry = 0; retry < MAX_RETRIES; retry++) {
            try {
                getStickerDetail(href, title, category);
                return; // 成功则返回
            } catch (Exception e) {
                if (retry == MAX_RETRIES - 1) {
                    throw e; // 最后一次重试失败则抛出异常
                }
                log.warn("获取贴图 {} 详情失败，第 {} 次重试", title, retry + 1);
                try {
                    Thread.sleep(1000 * (retry + 1)); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(ie);
                }
            }
        }
    }
    
    // 这里需要包含原有的 getStickerDetail 方法
    // 由于篇幅限制，这里省略具体实现
    // 实际使用时需要从 LineStore.java 中复制该方法
    private void getStickerDetail(String href, String title, Category category) {
        // 实现细节请参考 LineStore.java 中的 getStickerDetail 方法
        log.info("处理贴图详情: {}", title);
        // ... 具体实现
    }
}
