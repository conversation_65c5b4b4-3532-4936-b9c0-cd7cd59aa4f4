import com.alibaba.fastjson.JSON;
import com.apk.website.Application;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.TEmojiData;
import com.apk.website.entity.TStickerData;
import com.apk.website.mapper.TEmojiInfoDataMapper;
import com.apk.website.mapper.TStickerInfoDataMapper;
import com.apk.website.schedule.utils.QiniuUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import com.apk.website.vo.TStickerPreviewData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.codehaus.jackson.map.ObjectMapper;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
@Slf4j
public class LineStore {
    @Autowired
    QiniuUtils qiniuUtils;

    @Autowired
    private TStickerInfoDataMapper tStickerInfoDataMapper;

    @Autowired
    private TEmojiInfoDataMapper emojiInfoDataMapper;



    // 多线程配置
    private static final int THREAD_POOL_SIZE = 8; // 线程池大小
    private static final int CATEGORY_THREAD_POOL_SIZE = 4; // 分类爬取线程池大小
    private static final int DETAIL_THREAD_POOL_SIZE = 16; // 详情爬取线程池大小

    // 列表爬取url
    private static final String BASE_URL = "https://store.line.me/stickershop/showcase/top/zh-Hant?page=";
    // 列表的最大页数
    int MAX_PAGE = 103;


    public static void main(String[] args) throws Exception {

    }

    @Test
    // 单线程版本的分类爬取（原版本）
    public void getCategoryPathSingleThread() {
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/stickershop/showcase/top/zh-Hant";
        Request request = OkHttpClientUtils.buildRequest(url);
        try (Response response = clientInstance.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String body = response.body().string();
                Document parse = Jsoup.parse(body);
                Elements elements = parse.select("ul.mdCMN13Ul").first().select("li");
                elements.remove(0); // 移除全部这一行
                for (Element element : elements) {
                    Category category = new Category();
                    String href = element.select("a").attr("href");
                    String categoryName = element.select("a").text();
                    String count = element.select("em.mdCMN13Count").text().replace("(", "").replace(")", "").replace(",", "");
                    log.info("已爬取{}，链接：{}，数量：{}", categoryName, href, count);
                    category.setPath(href);
                    category.setCount(Integer.parseInt(count));
                    category.setTitle(categoryName);
                    category.setId(Integer.parseInt(href.replace("?category=", "")));

                    log.info(String.valueOf(category));
                    getStickerListByCategory(category); // 使用单线程版本
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Data
    static class Category {
        private String title;
        private int count;
        private String path;
        private int id;
    }

    @Data
    static class EmojiCategory {
        private String title;
        private String path;
        private int id;
    }

    @Test
    public void getList() {
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        for (int i = 0; i < MAX_PAGE; i++) {
            String url = BASE_URL + i;
            Request request = OkHttpClientUtils.buildRequest(url);
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String body = response.body().string();
                    Document parse = Jsoup.parse(body);
                    Elements elements = null;
                    try {
                        elements = parse.select("div.MdCMN02List").first().select("ul").first().select("li");
                    }catch (Exception e) {
                        log.info("爬取第{}页错误", i);
                        continue;
                    }
                    log.info("第{}页有{}个元素", i, elements.size());
                    log.info("开始爬取第{}页", i);
                    int j = 0;
                    for (Element element : elements) {

                        String href = element.select("a").attr("href");
                        log.info("href:{}", href);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }


    @Test
    // 获取分类链接 - 多线程版本
    public void getCategoryPath() {
        getCategoryPathMultiThread();
    }

    // 多线程版本的分类爬取
    public void getCategoryPathMultiThread() {
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/stickershop/showcase/top/zh-Hant";
        Request request = OkHttpClientUtils.buildRequest(url);

        List<Category> categories = new ArrayList<>();

        try (Response response = clientInstance.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String body = response.body().string();
                Document parse = Jsoup.parse(body);
                Elements elements = parse.select("ul.mdCMN13Ul").first().select("li");
                elements.remove(0); // 移除全部这一行
                // 先收集所有分类信息
                for (Element element : elements) {
                    Category category = new Category();
                    String href = element.select("a").attr("href");
                    String categoryName = element.select("a").text();
                    String count = element.select("em.mdCMN13Count").text().replace("(", "").replace(")", "").replace(",", "");
                    log.info("爬取{}，链接：{}，数量：{}", categoryName, href, count);
                    category.setPath(href);
                    category.setCount(Integer.parseInt(count));
                    category.setTitle(categoryName);
                    category.setId(Integer.parseInt(href.replace("?category=", "")));
                    categories.add(category);
                }

                // 使用线程池并行处理所有分类
                ExecutorService categoryExecutor = Executors.newFixedThreadPool(CATEGORY_THREAD_POOL_SIZE);
                CountDownLatch categoryLatch = new CountDownLatch(categories.size());
                AtomicInteger processedCategories = new AtomicInteger(0);

                log.info("开始多线程爬取 {} 个分类", categories.size());

                for (Category category : categories) {
                    categoryExecutor.submit(() -> {
                        try {
                            log.info("线程 {} 开始处理分类: {}", Thread.currentThread().getName(), category.getTitle());
                            getStickerListByCategoryMultiThread(category);
                            int processed = processedCategories.incrementAndGet();
                            log.info("分类 {} 处理完成，进度: {}/{}", category.getTitle(), processed, categories.size());
                        } catch (Exception e) {
                            log.error("处理分类 {} 时发生错误", category.getTitle(), e);
                        } finally {
                            categoryLatch.countDown();
                        }
                    });
                }

                try {
                    categoryLatch.await(); // 等待所有分类处理完成
                    log.info("所有分类爬取完成！");
                } catch (InterruptedException e) {
                    log.error("等待分类处理完成时被中断", e);
                    Thread.currentThread().interrupt();
                } finally {
                    categoryExecutor.shutdown();
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    // 根据分类获取链接 - 多线程版本
    public void getStickerListByCategoryMultiThread(Category category) {
        log.info("开始多线程爬取{}，链接：{}，数量：{}", category.getTitle(), category.getPath(), category.getCount());
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/stickershop/showcase/top/zh-Hant";
        int MAX_COUNT = 45; //最大爬取数量

        int MAX_PAGE = (int) Math.ceil((double) category.getCount() / 36);

        // 收集所有需要爬取的贴图信息
        List<StickerInfo> stickerInfos = new ArrayList<>();

        if (MAX_COUNT > 0) {
            log.info("限制最大爬取数量为: {}", MAX_COUNT);
            MAX_PAGE = (int) Math.ceil((double) MAX_COUNT / 36);
        }

        int i = 1;
        for (int pageNum = 1; pageNum <= MAX_PAGE; pageNum++) {
            log.info("开始爬取第{}个", i);
            Request request = OkHttpClientUtils.buildRequest(url + category.getPath() + "&page=" + pageNum);
            try (Response response = clientInstance.newCall(request).execute()) {
                i++;
                if (response.isSuccessful()) {
                    String body = response.body().string();
                    if (body.contains("您要求存取的網頁不存在。")) {
                        log.info("分类 {} 第 {} 页已爬完", category.getTitle(), pageNum);
                        break;
                    }
                    Document parse = Jsoup.parse(body);
                    Elements elements = parse.select("div.MdCMN02List").first().select("ul").first().select("li");
                    for (Element element : elements) {
                        String href = element.select("a").attr("href");
                        String title = element.select("a").select("p").text();
                        String stickerId = href.replaceAll(".*/stickershop/product/([0-9]+)/.*", "$1");
                        if (tStickerInfoDataMapper.selectById(stickerId) != null){
                            log.info("{}已存在",stickerId);
                            continue;
                        }
                        stickerInfos.add(new StickerInfo(href, title, category));
                    }
                } else {
                    log.info("分类 {} 第 {} 页爬取失败", category.getTitle(), pageNum);
                    break;
                }
            } catch (IOException e) {
                log.error("爬取分类 {} 第 {} 页时发生错误", category.getTitle(), pageNum, e);
                break;
            }
        }

        if (stickerInfos.isEmpty()) {
            log.info("分类 {} 没有新的贴图需要爬取", category.getTitle());
            return;
        }

        // 使用线程池并行处理贴图详情
        ExecutorService detailExecutor = Executors.newFixedThreadPool(DETAIL_THREAD_POOL_SIZE);
        CountDownLatch detailLatch = new CountDownLatch(stickerInfos.size());
        AtomicInteger processedStickers = new AtomicInteger(0);

        log.info("分类 {} 开始多线程爬取 {} 个贴图详情", category.getTitle(), stickerInfos.size());

        for (StickerInfo stickerInfo : stickerInfos) {
            detailExecutor.submit(() -> {
                try {
                    getStickerDetail(stickerInfo.href, stickerInfo.title, stickerInfo.category);
                    int processed = processedStickers.incrementAndGet();
                    if (processed % 10 == 0) { // 每处理10个打印一次进度
                        log.info("分类 {} 贴图处理进度: {}/{}", category.getTitle(), processed, stickerInfos.size());
                    }
                } catch (Exception e) {
                    log.error("处理贴图 {} 时发生错误", stickerInfo.title, e);
                } finally {
                    detailLatch.countDown();
                }
            });
        }

        try {
            detailLatch.await(); // 等待所有贴图详情处理完成
            log.info("分类 {} 的所有贴图爬取完成！总计: {}", category.getTitle(), stickerInfos.size());
        } catch (InterruptedException e) {
            log.error("等待分类 {} 贴图处理完成时被中断", category.getTitle(), e);
            Thread.currentThread().interrupt();
        } finally {
            detailExecutor.shutdown();
        }
    }

    // 贴图信息辅助类
    @Data
    static class StickerInfo {
        private String href;
        private String title;
        private Category category;

        public StickerInfo(String href, String title, Category category) {
            this.href = href;
            this.title = title;
            this.category = category;
        }
    }

    // 表情包信息辅助类
    @Data
    static class EmojiInfo {
        private String href;
        private String title;
        private EmojiCategory category;

        public EmojiInfo(String href, String title, EmojiCategory category) {
            this.href = href;
            this.title = title;
            this.category = category;
        }
    }

    // 单线程版本的分类爬取（保留原有逻辑作为备选）
    public void getStickerListByCategory(Category category) {
        log.info("开始爬取{}，链接：{}，数量：{}", category.getTitle(), category.getPath(), category.getCount());
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/stickershop/showcase/top/zh-Hant";

        int MAX_PAGE = (int) Math.ceil((double) category.getCount() / 36);
        for (int pageNum = 1; pageNum <= MAX_PAGE; pageNum++) {
            Request request = OkHttpClientUtils.buildRequest(url + category.getPath() + "&page=" + pageNum);
            try (Response response = clientInstance.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String body = response.body().string();
                    if (body.contains("您要求存取的網頁不存在。")) {
                        log.info("该分类已爬完");
                        break;
                    }
                    Document parse = Jsoup.parse(body);
                    Elements elements = parse.select("div.MdCMN02List").first().select("ul").first().select("li");
                    for (Element element : elements) {
                        String href = element.select("a").attr("href");
                        String title = element.select("a").select("p").text();
                        String stickerId = href.replaceAll(".*/stickershop/product/([0-9]+)/.*", "$1");
                        if (tStickerInfoDataMapper.selectById(stickerId) != null){
                            log.info("{}已存在",stickerId);
                            continue;
                        }
                        getStickerDetail(href, title, category);
                    }
                }else {
                    log.info("爬取失败");
                    break;
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    // 获取单个贴图详情 - 线程安全版本
    public synchronized void getStickerDetail(String href, String title, Category category) {
        log.info("开始爬取{}，链接：{}", title, href);
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String baseUrl = "https://store.line.me";
        String url = baseUrl + href;
        Request request = OkHttpClientUtils.buildRequest(url);
        try (Response response = clientInstance.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String body = response.body().string();
                Document parse = Jsoup.parse(body);
                Element mainElement = parse.select("div.mdBox03Inner01").first();
                TStickerData stickerData = new TStickerData();

                String stickerId = href.replaceAll(".*/stickershop/product/([0-9]+)/.*", "$1");
                String stickerTitle = mainElement.select("p.mdCMN38Item01Ttl").text();
                String stickerCover = mainElement.select("div.mdCMN38Img > img").attr("src");
                log.info("正在将封面图上传到七牛云，stickerCover:{}", stickerCover);
                stickerCover = qiniuUtils.downloadAndUpload(stickerCover, "sticker/" + stickerId + "/cover", qiniuUtils.getQiniuToken(), 3);
                log.info("封面图上传成功，stickerCover:{}", stickerCover);
                String AuthorName = mainElement.select("a.mdCMN38Item01Author").text();
                String authorId = mainElement.select("a.mdCMN38Item01Author").select("a").attr("href").replaceAll(".*/author/(\\d+)/.*", "$1");
                String price = mainElement.select("p.mdCMN38Item01Price").text().replace("US$", "");
                String description = mainElement.select("p.mdCMN38Item01Txt").text();

                @Data class StickerData {
                    private String fallbackStaticUrl;
                    private String animationUrl;
                    private String popupUrl;
                    private String soundUrl;

                    public StickerData(String fallbackStaticUrl, String animationUrl, String popupUrl, String soundUrl) {
                        this.fallbackStaticUrl = fallbackStaticUrl;
                        this.animationUrl = animationUrl;
                        this.popupUrl = popupUrl;
                        this.soundUrl = soundUrl;
                    }
                }
                List<StickerData> stickersList = new ArrayList<>();

                Elements stickerElements = mainElement.select("ul.FnStickerList").first().select("li");
                for (Element stickerElement : stickerElements) {
                    String data = stickerElement.attr("data-preview");
                    ObjectMapper mapper = new ObjectMapper();
                    TStickerPreviewData stickerPreviewJson = mapper.readValue(data, TStickerPreviewData.class);
                    String animationUrl = stickerPreviewJson.getAnimationUrl();
                    String fallbackStaticUrl = stickerPreviewJson.getFallbackStaticUrl();
                    String popupUrl = stickerPreviewJson.getPopupUrl();
                    String soundUrl = stickerPreviewJson.getSoundUrl();
                    String token = qiniuUtils.getQiniuToken();

                    if (!animationUrl.isEmpty()) {
                        String AnimationQiNiuKey = "sticker/" + stickerId + "/" + animationUrl.replaceAll(".*/sticker/(\\d+)/.*", "$1") + "_animation";
                        log.info("正在将动图上传到七牛云，animationUrl:{}", animationUrl);
                        animationUrl = qiniuUtils.downloadAndUpload(animationUrl,AnimationQiNiuKey, token, 3);
                        log.info("动图上传成功，animationUrl:{}", animationUrl);
                    }

                    if (!popupUrl.isEmpty()) {
                        String popupQiNiuKey = "sticker/" + stickerId + "/" + popupUrl.replaceAll(".*/sticker/(\\d+)/.*", "$1") + "_popup";
                        log.info("正在将popup上传到七牛云，popupUrl:{}", popupUrl);
                        popupUrl = qiniuUtils.downloadAndUpload(popupUrl, popupQiNiuKey, token, 3);
                        log.info("popup上传成功，popupUrl:{}", popupUrl);
                    }

                    if (!soundUrl.isEmpty()) {
                        String soundQiNiuKey = "sticker/" + stickerId + "/" + soundUrl.replaceAll(".*/sticker/(\\d+)/.*", "$1") + "_sound";
                        log.info("正在将sound上传到七牛云，soundUrl:{}", soundUrl);
                        soundUrl = qiniuUtils.downloadAndUpload(soundUrl, soundQiNiuKey, token, 3);
                        log.info("sound上传成功，soundUrl:{}", soundUrl);
                    }

                    if (!fallbackStaticUrl.isEmpty()) {
                        String fallbackStaticQiNiuKey = "sticker/" + stickerId + "/" + fallbackStaticUrl.replaceAll(".*/sticker/(\\d+)/.*", "$1") + "_fallbackStatic";
                        log.info("正在将静态图上传到七牛云，fallbackStaticUrl:{}", fallbackStaticUrl);
                        fallbackStaticUrl = qiniuUtils.downloadAndUpload(fallbackStaticUrl, fallbackStaticQiNiuKey, token, 3);
                        log.info("静态图上传成功，fallbackStaticUrl:{}", fallbackStaticUrl);
                    }

                    stickersList.add(new StickerData(fallbackStaticUrl, animationUrl, popupUrl, soundUrl));
                }

                stickerData.setId(Integer.parseInt(stickerId));
                stickerData.setTitle(stickerTitle);
                stickerData.setCover(stickerCover);
                stickerData.setAuthorName(AuthorName);
                stickerData.setAuthorId(Integer.parseInt(authorId));
                stickerData.setPrice(new BigDecimal(price));
                stickerData.setDescription(description);
                stickerData.setCategoryId(category.getId());
                stickerData.setCategoryName(category.getTitle());
                stickerData.setStickersList(JSON.toJSONString(stickersList));
                stickerData.setType("0");
                stickerData.setCreateTime(new Date());
                stickerData.setUpdateTime(new Date());
                log.info("即将插入: {}", stickerData);
                try {
                    tStickerInfoDataMapper.insert(stickerData);
                    log.info("插入{}成功", stickerData.getTitle());
                } catch (DuplicateKeyException e) {
                    log.warn("已存在");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    //上面是获取贴图的----------------------------------------------------------------------------------
    @Test
    // 获表情贴分类链接 - 多线程版本
    public void getEmojiCategoryPath() {
        getEmojiCategoryPathMultiThread();
    }
    // 多线程版本的分类爬取
    public void getEmojiCategoryPathMultiThread() {
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/emojishop/showcase/top/zh-Hant";
        Request request = OkHttpClientUtils.buildRequest(url);

        List<EmojiCategory> categories = new ArrayList<>();

        try (Response response = clientInstance.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String body = response.body().string();
                Document parse = Jsoup.parse(body);
                Elements elements = parse.select("ul.mdCMN13Ul").first().select("li");
                elements.remove(1); // 移除新增这一行
                // 先收集所有分类信息
                for (Element element : elements) {
                    EmojiCategory category = new EmojiCategory();
                    String href = element.select("a").attr("href");
                    String categoryName = element.select("a").text();
                    log.info("爬取{}，链接：{}", categoryName, href);
                    category.setPath(href);
                    category.setTitle(categoryName);
                    category.setId(0);
                    categories.add(category);
                }

                log.info("开始多线程爬取 {} 个分类", categories.size());

                for (EmojiCategory category : categories) {
                    try {
                        log.info("线程 {} 开始处理分类: {}", Thread.currentThread().getName(), category.getTitle());
                        getEmojiListByCategoryMultiThread(category);
                        log.info("分类 {} 处理完成，进度: {}/{}", category.getTitle(), categories.size());
                    } catch (Exception e) {
                        log.error("处理分类 {} 时发生错误", category.getTitle(), e);
                    }
                }

                log.info("所有分类爬取完成！");

            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    // 根据分类获取链接 - 多线程版本
    public void getEmojiListByCategoryMultiThread(EmojiCategory category) {
        log.info("开始多线程爬取{}，链接：{}", category.getTitle(), category.getPath());
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/emojishop/showcase/top/zh-Hant";
        int MAX_PAGE = 15; //最大爬取数量


        // 收集所有需要爬取的贴图信息
        List<EmojiInfo> EmojiInfo = new ArrayList<>();

        if (MAX_PAGE > 0) {
            log.info("限制最大爬取页数为: {}", MAX_PAGE);
        }

        int i = 1;
        for (int pageNum = 3; pageNum <= MAX_PAGE; pageNum++) {
            log.info("开始爬取第{}个", i);
            Request request = OkHttpClientUtils.buildRequest(url + "?page=" + pageNum);
            try (Response response = clientInstance.newCall(request).execute()) {
                i++;
                if (response.isSuccessful()) {
                    String body = response.body().string();
                    if (body.contains("您要求存取的網頁不存在。")) {
                        log.info("分类 {} 第 {} 页已爬完", category.getTitle(), pageNum);
                        break;
                    }
                    Document parse = Jsoup.parse(body);
                    Elements elements = parse.select("div.MdCMN02List").first().select("ul").first().select("li");
                    for (Element element : elements) {
                        String href = element.select("a").attr("href");
                        String title = element.select("a").select("p").text();
                        String emojiId = href.replaceAll(".*/emojishop/product/([0-9a-z]+)/.*", "$1");
                        if (emojiInfoDataMapper.selectById(emojiId) != null){
                            log.info("{}已存在",emojiId);
                            continue;
                        }
                        EmojiInfo.add(new EmojiInfo(href, title, category));
                    }
                } else {
                    log.info("分类 {} 第 {} 页爬取失败", category.getTitle(), pageNum);
                    break;
                }
            } catch (IOException e) {
                log.error("爬取分类 {} 第 {} 页时发生错误", category.getTitle(), pageNum, e);
                break;
            }
        }

        if (EmojiInfo.isEmpty()) {
            log.info("分类 {} 没有新的贴图需要爬取", category.getTitle());
            return;
        }


        log.info("分类 {} 开始多线程爬取 {} 个贴图详情", category.getTitle(), EmojiInfo.size());

        for (EmojiInfo emojiInfo : EmojiInfo) {
            try {
                getEmojiDetail(emojiInfo.href, emojiInfo.title, emojiInfo.category);
            } catch (Exception e) {
                log.error("处理表情包 {} 时发生错误", emojiInfo.title, e);
            }
        }

        log.info("分类 {} 的所有表情包爬取完成！总计: {}", category.getTitle(), EmojiInfo.size());
    }

    // 获取单个贴图详情 - 线程安全版本
    public synchronized void getEmojiDetail(String href, String title, EmojiCategory category) {
        log.info("开始爬取{}，链接：{}", title, href);
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String baseUrl = "https://store.line.me";
        String url = baseUrl + href;
        Request request = OkHttpClientUtils.buildRequest(url);
        try (Response response = clientInstance.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String body = response.body().string();
                Document parse = Jsoup.parse(body);
                Element mainElement = parse.select("div.mdBox03Inner01").first();
                TEmojiData emojiData = new TEmojiData();

                String stickerId = href.replaceAll(".*/emojishop/product/([0-9a-z]+)/.*", "$1");
                String stickerTitle = mainElement.select("p.mdCMN38Item01Ttl").text();
                String stickerCover = mainElement.select("div.mdCMN38Img > img").attr("src");
                log.info("正在将封面图上传到七牛云，stickerCover:{}", stickerCover);
                stickerCover = qiniuUtils.downloadAndUpload(stickerCover, "emoji/" + stickerId + "/cover", qiniuUtils.getQiniuToken(), 3);
                log.info("封面图上传成功，stickerCover:{}", stickerCover);
                String AuthorName = mainElement.select("a.mdCMN38Item01Author").text();
                String authorId = mainElement.select("a.mdCMN38Item01Author").select("a").attr("href").replaceAll(".*/author/(\\d+)/.*", "$1");
                String price = mainElement.select("p.mdCMN38Item01Price").text().replace("US$", "");
                String description = mainElement.select("p.mdCMN38Item01Txt").text();

                @Data class EmojiData {
                    private String fallbackStaticUrl;
                    private String animationUrl;

                    public EmojiData(String fallbackStaticUrl, String animationUrl) {
                        this.fallbackStaticUrl = fallbackStaticUrl;
                        this.animationUrl = animationUrl;
                    }
                }
                List<EmojiData> emojisList = new ArrayList<>();

                Elements stickerElements = mainElement.select("ul.mdCMN09Ul").first().select("li");
                for (Element stickerElement : stickerElements) {
                    String data = stickerElement.attr("data-preview");
                    ObjectMapper mapper = new ObjectMapper();
                    TStickerPreviewData stickerPreviewJson = mapper.readValue(data, TStickerPreviewData.class);
                    String animationUrl = stickerPreviewJson.getAnimationUrl();
                    String fallbackStaticUrl = stickerPreviewJson.getFallbackStaticUrl();
                    String token = qiniuUtils.getQiniuToken();

                    if (!animationUrl.isEmpty()) {
                        // 通过分割字符串提取
                        String[] parts = animationUrl.split("/");
                        String lastPart = parts[parts.length - 1]; // "040.png?v=2"
                        String number = lastPart.split("\\.")[0];  // "040"
                        String AnimationQiNiuKey = "emoji/" + stickerId + "/" + number;
                        log.info("正在将动图上传到七牛云，animationUrl:{}", animationUrl);
                        animationUrl = qiniuUtils.downloadAndUpload(animationUrl,AnimationQiNiuKey, token, 3);
                        log.info("动图上传成功，animationUrl:{}", animationUrl);
                    }

                    if (!fallbackStaticUrl.isEmpty()) {
                        // 通过分割字符串提取
                        String[] parts = fallbackStaticUrl.split("/");
                        String lastPart = parts[parts.length - 1]; // "040.png?v=2"
                        String number = lastPart.split("\\.")[0];  // "040"
                        String fallbackStaticQiNiuKey = "emoji/" + stickerId + "/" + number + "_fallbackStatic";
                        log.info("正在将静态图上传到七牛云，fallbackStaticUrl:{}", fallbackStaticUrl);
                        fallbackStaticUrl = qiniuUtils.downloadAndUpload(fallbackStaticUrl, fallbackStaticQiNiuKey, token, 3);
                        log.info("静态图上传成功，fallbackStaticUrl:{}", fallbackStaticUrl);
                    }

                    emojisList.add(new EmojiData(fallbackStaticUrl, animationUrl));
                }

                emojiData.setId(stickerId);
                emojiData.setTitle(stickerTitle);
                emojiData.setCover(stickerCover);
                emojiData.setAuthorName(AuthorName);
                emojiData.setAuthorId(Integer.parseInt(authorId));
                emojiData.setPrice(new BigDecimal(price));
                emojiData.setDescription(description);
                emojiData.setCategoryId(category.getId());
                emojiData.setCategoryName(category.getTitle());
                emojiData.setStickersList(JSON.toJSONString(emojisList));
                emojiData.setType("0");
                emojiData.setCreateTime(new Date());
                emojiData.setUpdateTime(new Date());
                log.info("即将插入: {}", emojiData);
                try {
                    emojiInfoDataMapper.insert(emojiData);
                    log.info("插入{}成功", emojiData.getTitle());
                } catch (DuplicateKeyException e) {
                    log.warn("已存在");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
