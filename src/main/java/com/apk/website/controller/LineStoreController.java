package com.apk.website.controller;

import com.apk.website.common.vo.PageData;
import com.apk.website.dto.linestore.LineStoreDetailReq;
import com.apk.website.dto.linestore.LineStoreListReq;
import com.apk.website.dto.linestore.LineStoreSearchReq;
import com.apk.website.service.LineStoreService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequiredArgsConstructor
@RequestMapping("/api/line")

public class LineStoreController {

    private  final LineStoreService lineStoreService;

    @PostMapping("/list")
    public BaseResponse stickerList(@RequestBody LineStoreListReq req) {
        PageData<T> result = lineStoreService.getStickerList(req);
        return  BaseResponse.success(result);
    }


    @PostMapping("/search")
    public BaseResponse searchSticker(@RequestBody LineStoreSearchReq req) {
        List<T> result = lineStoreService.search(req);
        return  BaseResponse.success(result);
    }

    @PostMapping("/detail")
    public BaseResponse detail(@RequestBody LineStoreDetailReq req) {
        return BaseResponse.success(lineStoreService.detail(req));
    }
}
