package com.apk.website.schedule;

import com.alibaba.fastjson.JSON;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.*;
import com.apk.website.mapper.*;
import com.apk.website.schedule.utils.QiniuUtils;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;

@Component
@Slf4j
public class CarsSchedule {
    @Autowired
    QiniuUtils qiniuUtils;
    @Autowired
    TCarInfoMapper carInfoMapper;
    @Autowired
    TLocationMapper locationMapper;
    @Autowired
    TCarInfoMapper tCarInfoMapper;
    @Autowired
    TCarTipMapper carTipMapper;
    @Autowired
    TMajorCityLocationMapper majorCityLocationMapper;
    @Autowired
    TCarBillMapper carBillMapper;
    final static ExecutorService executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors()*2, Runtime.getRuntime().availableProcessors()*4, 10, java.util.concurrent.TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());
    final static ExecutorService executorService2 = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors()*2, Runtime.getRuntime().availableProcessors()*4, 10, java.util.concurrent.TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

    @Data
    static class DetailTag {
        private String tag;
        private String text;
    }

    @SneakyThrows
    public static void main(String[] args) {
//        int i = Runtime.getRuntime().availableProcessors();

        CarsSchedule carsSchedule = new CarsSchedule();
//        carsSchedule.pull("https://car.carslyst.com/used-cars/", "Used Car Listings");
//        carsSchedule.pullPopular();
//        carsSchedule.pullDealer("https://car.carslyst.com/car-dealers/",  "Local Car Dealers", "");
//        carsSchedule.pullDealerPopular("https://car.carslyst.com/car-dealers/make/","Popular Car Makes");
//        carsSchedule.pullRepair("https://car.carslyst.com/car-repair/", "Repair Shop Locator");
//        carsSchedule.pullRepairService();
//        carsSchedule.pullRepairPopular();
//        carsSchedule.pullAuction();
//        carsSchedule.pullGovAuction();
//        carsSchedule.pullCarTools();
//        carsSchedule.pullVinLookup();
//        carsSchedule.pullSaleBill();
//        carsSchedule.pullLoanCalculator();
//        carsSchedule.pullTips("https://car.carslyst.com/car-tips/");

        carsSchedule.pullMajorCityLocation();
    }
    public void pull(String url,String subCategory) {
        Request request = buildRequest(url);
        try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
            if (response.isSuccessful()) {
                String statesHtmlText = response.body().string();
                if (statesHtmlText.isEmpty()){
                    return;
                }
                Elements states = extractState(statesHtmlText);
                ArrayList<Future<?>> futures = new ArrayList<>();
                for (Element element : states) {
                    futures.add(executorService2.submit(() -> {
                        try {
                            String href = element.attr("href");
                            String state = element.select("span").text();
                            Request request1 = buildRequest("https://car.carslyst.com"+href);
                            try (Response response1 = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request1).execute()) {
                                if (response1.isSuccessful()) {
                                    //如果是popular进来就是文章列表了
                                    if("Popular Car Makes".equals(subCategory)){
                                        String articlesHtmlText = response1.body().string();
                                        //type不知道(车型,比如SUV)
                                        resolveArticles(articlesHtmlText,href, state, "",subCategory);
                                        return;
                                    }
                                    String typeHtmlText = response1.body().string();
                                    if (typeHtmlText.isEmpty()) {
                                       return;
                                    }
                                    Document typeDoc = Jsoup.parse(typeHtmlText);
                                    Elements typeElements = typeDoc.select("ul.general-list.brand-list.type").select("a.brand-name");
                                    for (Element typeElement : typeElements) {
                                        // 获取类型名称
                                        String type = typeElement.text();
                                        String typeHref = typeElement.attr("href");
                                        Request request2 = buildRequest("https://car.carslyst.com"+typeHref);
                                        try (Response response2 = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request2).execute()){
                                            if (response2.isSuccessful()) {
                                                String articlesHtmlText = response2.body().string();
                                                resolveArticles(articlesHtmlText,typeHref, state, type,subCategory);
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }));
                    Thread.sleep(4000);
                }
                for (Future<?> future : futures) {
                    future.get();
                }

            }
        } catch (Exception e) {
            log.error("请求失败{}", e.getMessage());
        }}
    public void pullPopular() {
        Request request = buildRequest("https://car.carslyst.com/car-makes/");
        try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
            if (response.isSuccessful()) {
                String brandHtmlText = response.body().string();
                // 获取所有品牌列表
                Elements brands = extractBrand(brandHtmlText);
                for (Element element : brands) {
                    // 获取品牌图片
//                    String imgUrl = element.select("img").attr("data-src");
                    //imgUrl=qiniuUtils.downloadAndUpload(imgUrl, UUID.randomUUID().toString(), qiniuUtils.getQiniuToken(), 3);
                    Elements a = element.select("a.brand-name");
                    String href = a.attr("href");
                    pull("https://car.carslyst.com"+href, "Popular Car Makes");
                    //String brand = a.text();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Request buildRequest(String url) {
        Request.Builder builder = new Request.Builder().url(url);

        builder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3");
        builder.addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        return builder.build();
    }
    private Request buildRequestCars(String url) {
        Request.Builder builder = new Request.Builder().url(url);
        builder.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .addHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,sq;q=0.7")
                .addHeader("cache-control", "no-cache")
                .addHeader("content-type", "application/x-www-form-urlencoded")
                .addHeader("origin", "https://car.carslyst.com")
                .addHeader("pragma", "no-cache")
                .addHeader("priority", "u=0, i")
                .addHeader("referer", "https://car.carslyst.com/used-cars/mo/?__cf_chl_tk=kHCkmXGklDnqoSWvOX.2A3u7LaLZ2jPQjyqa2mPjKVc-1756087640-1.0.1.1-1LbNDSll5MMjfZi2ZRrH_59bAcqOEcssWCWx18Ie_AQ")
                .addHeader("sec-ch-ua", "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"")
                .addHeader("sec-ch-ua-arch", "\"arm\"")
                .addHeader("sec-ch-ua-bitness", "\"64\"")
                .addHeader("sec-ch-ua-full-version", "\"139.0.7258.128\"")
                .addHeader("sec-ch-ua-full-version-list", "\"Not;A=Brand\";v=\"99.0.0.0\", \"Google Chrome\";v=\"139.0.7258.128\", \"Chromium\";v=\"139.0.7258.128\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-model", "\"\"")
                .addHeader("sec-ch-ua-platform", "\"macOS\"")
                .addHeader("sec-ch-ua-platform-version", "\"15.6.0\"")
                .addHeader("sec-fetch-dest", "document")
                .addHeader("sec-fetch-mode", "navigate")
                .addHeader("sec-fetch-site", "same-origin")
                .addHeader("sec-fetch-user", "?1")
                .addHeader("upgrade-insecure-requests", "1")
                .addHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36")
                .addHeader("Cookie", "_gcl_au=1.1.905061801.1755583231; _ga=GA1.1.487083986.1755583231; __gads=ID=584785ac636ee123:T=1755583232:RT=1756114947:S=ALNI_MZ4MIm9GEiGCr274-VTrCXoXNSv7g; __gpi=UID=00001182a07af18d:T=1755583232:RT=1756114947:S=ALNI_MZjwxU0Im9dl0U7zh8i_HN3JAk3dA; __eoi=ID=b2f5be9fa78d8385:T=1755583232:RT=1756114947:S=AA-AfjZoENJuKqP0F4KynznDWmzS; _uetsid=45802120815811f095ee956823225a76; _uetvid=40c762603b9611f0879f1997d6bc2bde; _ga_JZN9ZP0P8X=GS2.1.s1756114230$o9$g1$t1756115035$j57$l0$h0; _ga_G3PVKYKNNM=GS2.1.s1756114230$o9$g1$t1756115035$j57$l0$h0; FCNEC=%5B%5B%22AKsRol-OORrJkPKyKQrB2oiPeIesMEgEmLSoaxfKyw-0F8YUOG3vf1AFe-7_DT9jNHQBi50uHUcArzA3sN-ldCQqFGI1Iep86GHSN7JfCQobRc7Ih9qYrvDdQ286XuVAEoCjixWtsaB4EZrN_J-tmlHPBQhp-ltM6g%3D%3D%22%5D%5D; _ga_EDR2NBFT2C=GS2.1.s1756114229$o9$g1$t1756115037$j53$l0$h0; cf_clearance=sLF7g1hZVOze7spJYrDycjso9TBle8IwfwP7Wl0ibrM-1756115049-*******-_zfzFZOY7XCaZk2gDOJqjynZXsnFKOIZ2geoC9.ykl9pgQ5qImLvodfupLCvPeDVHhRI.b4xYjm5uhqstWGj_g59OlU0.Mi2GPl6tp.Al0Tl7zFqmsUUC6KosQBnjzTllFtSf8BJgWxBdqZsRU8TFRJ9dVeYe3DWeF6nMgVpvhBHAAqIqxcm9elIr8OQUPCoTaVyLIQ0F5_LorvbFgCQjwiYiFQBuiZmunXgklGHbbU");
        return builder.build();
    }
    private void carsList(String  typeHref, String state, String type, Integer page,String subCategory){
        Request request2 = buildRequest("https://car.carslyst.com"+typeHref+ "?page="+page);
        try (Response response2 = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request2).execute()) {
            if (response2.isSuccessful()) {
                String articlesHtmlText = response2.body().string();
                Document carsDoc = Jsoup.parse(articlesHtmlText);
                Elements carsElements = carsDoc.select(".car-list .car-con div.car-item");
                ArrayList<TCarInfo> tCarInfos = new ArrayList<>();
                for (Element carElement : carsElements) {
                    TCarInfo tCarInfo = new TCarInfo();
                    tCarInfo.setState(state);
                    tCarInfo.setType(type);
                    tCarInfo.setSubCategory(subCategory);
                    tCarInfo.setCategory("Used Cars");
                    //获取title
                    String title = carElement.select(".car-title").text();
                    //获取location
                    String location = carElement.select(".car-info-item").get(0).select( "span").text();
                    String city =location.split(",")[0];
                    // 获取date
                    String date = carElement.select(".car-info-item").get(1).select( "span").text();
                    //获取价格
                    String price = carElement.select(".car-total").text();
                    //获取图片
                    String imgUrl = carElement.select(".car-pic img").attr("data-src");

                    tCarInfo.setTitle(title);
                    tCarInfo.setLocation(location);
                    tCarInfo.setCity(city);
                    tCarInfo.setDate(date);
                    if (StringUtils.hasText(price)){
                        tCarInfo.setPrice(Integer.parseInt(price));
                    }
                    imgUrl=uploadImg(imgUrl);
                    tCarInfo.setImg(imgUrl);
                    tCarInfo.setCover(imgUrl);
                    //获取carDetail链接
                    String carHref = carElement.select("a").attr("href");
                    Request carRequest = buildRequest("https://car.carslyst.com"+carHref);
                    try (Response response3 = OkHttpClientUtils.CLIENT.getClientInstance().newCall(carRequest).execute()) {
                        if (response3.isSuccessful()) {
                            String carHtmlText = response3.body().string();
                            Document carDoc = Jsoup.parse(carHtmlText);
                            Element carDetailElement = carDoc.select("div.features-content").get(0);
                            String carDetail = carDetailElement.toString();
                            tCarInfo.setDetailInfo( carDetail);
                            //获取brand
                            String brand = carDetailElement.select("p").get(4).text().split( " ")[1];
                            tCarInfo.setBrand(brand);
                            //获取description
                            String description = carDoc.select(".features-des").toString();
                            tCarInfo.setDescription(description);
                            tCarInfos.add(tCarInfo);
                        }
                    }

                }
                carInfoMapper.insertBatch(tCarInfos);
            }
        } catch (IOException e) {
            log.error("获取车辆详情Error:{}",  e.getMessage());
        }
    }

    private void resolveArticles(String articlesHtmlText, String typeHref, String state, String type,String subCategory){
        Document pageDoc = Jsoup.parse(articlesHtmlText);
        //获取总页数
        Elements select = pageDoc.select("div.brpp_pageGo").select("a.page-nor");
        String totalPage= "1";
        if(select.isEmpty()){
            totalPage = "1";
        }else{
            totalPage = select.last().text();
        }
        ArrayList<Future> futures = new ArrayList<>();
        for (int i = 1; i <= Integer.parseInt(totalPage); i++) {
            int finalI = i;
//            carsList(typeHref, state, type, finalI);
            futures.add(executorService.submit(() ->carsList(typeHref, state, type, finalI,subCategory)));
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        for (Future future : futures) {
            try {
                future.get();
            }catch (InterruptedException | ExecutionException e){
                log.error("任务执行失败", e);
            }
        }
    }

    private Elements extractState(String statesHtmlText){
        Document subCategoryDoc = Jsoup.parse(statesHtmlText);
        // 获取所有state列表
        Element ul = subCategoryDoc.select("ul.general-list.state-list").get( 0);
        return ul.select("a");
    }
    private Elements extractBrand(String brandHtmlText){
        Document subCategoryDoc = Jsoup.parse(brandHtmlText);
        // 获取所有品牌列表
        Element ul = subCategoryDoc.select("ul.general-list.brand-list.order").get( 0);
        Elements li = ul.select("li.general-item");
        return li;
    }


    public void pullDealer(String url ,String subCategory,String brand,String category){
        Request request = buildRequest(url);
        try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
            if (response.isSuccessful()) {
                //state
                String stateHtmlText = response.body().string();
                Elements states = extractState(stateHtmlText);
                ArrayList<Future> futures = new ArrayList<>();
                for (Element element : states) {
                    futures.add(executorService.submit(()->{
                        String href = element.attr("href");
                        String state = element.select("span").text();
                        Request request1 = buildRequest("https://car.carslyst.com"+href);
                        try(Response response1 = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request1).execute()) {
                            if (response1.isSuccessful()) {
                                String subCategoryHtmlText = response1.body().string();
                                //城市
                                Elements citys = extractState(subCategoryHtmlText);
                                ArrayList<Future> futures1 = new ArrayList<>();
                                for (Element city : citys) {
                                    futures1.add(executorService2.submit(()->{
                                        String cityHref = city.attr("href");
                                        String cityName = city.select("span").text();
                                        Request request2 = buildRequest("https://car.carslyst.com"+cityHref);
                                        try(Response response2 = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request2).execute()) {
                                            if (response2.isSuccessful()) {
                                                String dealersHtmlText = response2.body().string();
                                                Document dealersDoc = Jsoup.parse(dealersHtmlText);
                                                Elements locations = dealersDoc.select("li.office-location-item");
                                                ArrayList<TLocation> tLocations = new ArrayList<>();
                                                for (Element element1 : locations){
                                                    TMajorCityLocation tMajorCityLocation = new TMajorCityLocation();

                                                    Elements img = element1.select("div.location-pic").select("img");
                                                    String imgUrl = img.attr("data-src");

                                                    imgUrl=uploadImg(imgUrl);
                                                    String locationHref = element1.select(".office-location-link").select("a").attr("href");
                                                    //进入location详情页
                                                    String locationHtmlText = getHtmlText("https://car.carslyst.com" + locationHref);
                                                    if (locationHtmlText.isEmpty()){
                                                        continue;
                                                    }

                                                    extractLocation(locationHtmlText,tMajorCityLocation);
                                                    TLocation location = new TLocation();
                                                    BeanUtils.copyProperties(tMajorCityLocation,location);
                                                    location.setImg(imgUrl);
                                                    location.setSubCategory(subCategory);
                                                    location.setState(state);
                                                    location.setCity(cityName);
                                                    location.setCategory(category);
                                                    if("Common Services".equals(subCategory)){
                                                        location.setCommonService(brand);
                                                    }else {
                                                        location.setBrand(brand);
                                                    }
                                                    tLocations.add(location);
                                                }
                                                int i = locationMapper.insertBatch(tLocations);
                                            }
                                        }catch (IOException e){
                                            e.printStackTrace();
                                        }
                                    }));
                                }
                                for (Future<?> future : futures1){
                                    future.get();
                                }
                            }
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    }));
                }
                for (Future<?> future : futures){
                    future.get();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void pullDealerPopular(String url,  String subCategory,String category){
        Request request = buildRequest(url);
        try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
            if (response.isSuccessful()) {
                String brandHtmlText = response.body().string();
                // 获取所有品牌列表
                Elements brands = extractBrand(brandHtmlText);
                for (Element element : brands) {
                    // 获取品牌图片
//                    String imgUrl = element.select("img").attr("data-src");
//                    imgUrl=uploadImg(imgUrl);
                    Elements a = element.select("a.brand-name");
                    String href = a.attr("href");
                    String brand = a.text();
                    pullDealer("https://car.carslyst.com"+href, subCategory,brand,category);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void pullRepair(String url ,String subCategory){
        pullDealer( url ,subCategory,"","Car Repair");
    }
    public void pullRepairPopular(){
        pullDealerPopular("https://car.carslyst.com/car-repair/make/","Popular Car Makes","Car Repair");
    }
    public void pullRepairService(){
        String url = "https://car.carslyst.com/car-repair/service/";
        Request request = buildRequest(url);
        try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
            if (response.isSuccessful()) {
                String brandHtmlText = response.body().string();
                // 获取所有commonService列表
                Elements brands = extractBrand(brandHtmlText);
                for (Element element : brands) {
                    Elements a = element.select("a.brand-name");
                    String href = a.attr("href");
                    String service = a.text();
                    pullDealer("https://car.carslyst.com"+href, "Common Services",service,"Car Repair");
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    private void extractStore(String storesHtmlText,String state,String subCategory){
        Document  storeDoc = Jsoup.parse(storesHtmlText);
        // 获取所有store列表
        Elements stores = storeDoc.select("ul.general-list.store-list > li.store-item, ul.general-list.store-list > li.online-item");
        ArrayList<TLocation> tLocations = new ArrayList<>();
        for (Element store : stores) {
            TLocation location = new TLocation();
            if("Non-gov Auctions".equals(subCategory)){
                String href = "https://car.carslyst.com"+store.select("a.store-link").attr("href");
                String htmlText = getHtmlText(href);
                if(!StringUtils.hasText(htmlText)){
                    continue;
                }
                TMajorCityLocation tMajorCityLocation = new TMajorCityLocation();
                extractLocation(htmlText,tMajorCityLocation);
                BeanUtils.copyProperties(tMajorCityLocation,location);
                location.setSubCategory(subCategory);
                location.setState(state);
            }
            else {
                String title = store.select("div.online-content .online-top .online-text div.online-text-brand").text();
                String description = store.select(".online-top .online-text .online-text-des").text();

                String href = "https://car.carslyst.com"+store.select("a.link-cover").attr("href");
                String locationHtmlText = getHtmlText(href);
                if(!StringUtils.hasText(locationHtmlText)){
                    continue;
                }
                Document locationDoc = Jsoup.parse(locationHtmlText);
                String imgUrl = locationDoc.select(".online-img-con").select("img").attr("data-src");

                imgUrl=uploadImg(imgUrl);
                //imgUrl=qiniuUtils.downloadAndUpload(imgUrl, UUID.randomUUID().toString(), qiniuUtils.getQiniuToken(), 3);
                String netAddress = locationDoc.select("table").select("tr").get(0).select("td").get(1).text();
                String phone = locationDoc.select("table").select("tr").get(1).select("td").get(1).text();
                String date = locationDoc.select("table").select("tr").get(2).select("td").get(1).text();

                location.setTitle(title);
                location.setDescription(description);
                location.setImg(imgUrl);
                location.setNetAddress(netAddress);
                location.setPhone(phone);
                location.setDate(date);
                location.setSubCategory(subCategory);
            }
            location.setCategory("Car Auctions");
            tLocations.add(location);
        }
        locationMapper.insertBatch(tLocations);
    }


    public void pullAuction(){
        String url = "https://car.carslyst.com/car-auctions/";
        Request request = buildRequest(url);
        try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
            if (response.isSuccessful()) {
                String stateHtmlText = response.body().string();
                //  获取所有state列表
                Elements states = extractState(stateHtmlText);
                for (Element element : states) {
                    String href = element.attr("href");
                    String state = element.select("span").text();
                    Request request1 = buildRequest("https://car.carslyst.com"+href);
                    try(Response response1 = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request1).execute()) {
                        if (response1.isSuccessful()) {
                            String storesHtmlText = response1.body().string();
                            if (storesHtmlText.contains("No results found")||!StringUtils.hasText(storesHtmlText)){
                                continue;
                            }
                            Document parse = Jsoup.parse(storesHtmlText);
                            //获取总页数
                            Elements select = parse.select(".page-nor").select("a");
                            if (select==null|| select.isEmpty()){
                                executorService.execute(()->extractStore(storesHtmlText,state,"Non-gov Auctions"));
                                continue;
                            }
                            int pageCount =Integer.parseInt(select.last().text());
                            for (int i = 1; i <= pageCount; i++){
                                System.out.println("正在处理："+state+"第"+i+"页");
                                int finalI = i;
                                executorService.submit(() -> {
                                    String htmlText = getHtmlText("https://car.carslyst.com" + href + "?page=" + finalI);
                                    if (htmlText.contains("No results found")||!StringUtils.hasText(htmlText)){
                                        return;
                                    }
                                    extractStore(htmlText,state,"Non-gov Auctions");});
                                try {
                                    Thread.sleep(2000);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }catch (IOException e){
                        e.printStackTrace();
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void pullGovAuction(){
        String url = "https://car.carslyst.com/car-auctions/gov-online/";
        Request request = buildRequest(url);
        try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
            if (response.isSuccessful()) {
                String storesHtmlText = response.body().string();
                extractStore( storesHtmlText, "",  "Gov Auctions");
            }
        }catch (IOException e){
            e.printStackTrace();
        }
    }
    public void pullCarTools(){
        String url = "https://car.carslyst.com/car-tools/";
        String carToolsHtmlText = getHtmlText(url);
        Document carToolsDoc = Jsoup.parse(carToolsHtmlText);
        String title = carToolsDoc.select("div.container.detail").select("h1").text();
        Element content = carToolsDoc.select(".bottom .container. general-wrap").first();
        String title2 = content.select("h2").text();
        Elements tools = content.select("li.general-tool-item");
        for (Element tool : tools) {
            String imgSrc = "https://car.carslyst.com"+tool.select("general-tool-img").select("img").attr("src");
            //imgSrc=qiniuUtils.downloadAndUpload(imgSrc, UUID.randomUUID().toString(), qiniuUtils.getQiniuToken(), 3);
            String toolName = tool.select("span.general-tool-name").text();
        }
    }
    public void pullVinLookup(){
        String url = "https://car.carslyst.com/car-vin-lookup/";
        String vinHtmlText = getHtmlText(url);
        Document vinLookupDoc = Jsoup.parse(vinHtmlText);
        Elements elements = vinLookupDoc.select("div.vin-content div.vin-item");
    }

    private String uploadImg(String imgUrl) {

        String imgName = null;
        try {
            if (!StringUtils.hasText(imgUrl)){
                return "404.png";
            }
            if(!imgUrl.contains("static/Images")){
                return "404.png";
            }
             imgName = UUID.randomUUID().toString();
            imgName = qiniuUtils.downloadAndUpload(imgUrl, imgName, qiniuUtils.getQiniuToken(), 1);
            log.info("图片上传成功：{}", imgName);
        } catch (Exception e) {
            return "404.png";
        }
        return imgName;
    }
    public void pullSaleBill(){
        String url = "https://car.carslyst.com/bill-of-sale-for-car/";
        String safeBillHtmlText = getHtmlText(url);
        //提取州
        Document subCategoryDoc = Jsoup.parse(safeBillHtmlText);
        // 获取所有state列表
        Element ul = subCategoryDoc.select("ul.general-list.vin-state-list").get( 0);
        Elements states= ul.select("a");
        ArrayList<TCarBill> tCarBills = new ArrayList<>();
        for (Element element : states) {
            TCarBill tCarBill = new TCarBill();
            String href = element.attr("href");
            String state = element.select("span").text();
            String detailHtmlText = getHtmlText("https://car.carslyst.com" + href);
            if (detailHtmlText.contains("No results found")||!StringUtils.hasText(detailHtmlText)){
                continue;
            }
            Document  detailDoc = Jsoup.parse(detailHtmlText);

            //获取标题
            String title = detailDoc.select("div.container.detail").select("h1").text();
            Element imgElement = detailDoc.select("img.home-pdf-img").get(0);

            String imgUrl=imgElement.attr("data-src");
            imgUrl=uploadImg(imgUrl);
            tCarBill.setImg(imgUrl);
            tCarBill.setTitle(title);
            tCarBill.setState(state);
            tCarBill.setSubCategory("Bill of Sale for Car");
            tCarBills.add(tCarBill);
            log.info("carBill:{}",tCarBill);
        }
        int i = carBillMapper.insertBatch(tCarBills);

    }
    public void pullLoanCalculator(){
        String url = "https://car.carslyst.com/car-loan-calculator/";
        String loanCalculatorHtmlText = getHtmlText(url);

        Document loanCalculatorDoc = Jsoup.parse(loanCalculatorHtmlText);
        String img = "https://car.carslyst.com"+loanCalculatorDoc.select("div.cal-detail-icon").select("img").attr("src");
        String tableHtml = loanCalculatorDoc.select("div.bank-table").toString();
        String faqsHtml=loanCalculatorDoc.select("div.container div.general-wrap").get(2).toString();
    }
    public String getHtmlText(String url) {
        Request request = buildRequestCars(url);
        try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body().string();
            }
        }catch (IOException e){
            log.error("获取响应失败：{}",e.getMessage());
        }
        return "";
    }

    public void pullTips(String url){
//        String tipsHtmlText = getHtmlText(url);

        String s="<ul class=\"article-page grid\" id=\"article-page\" style=\"height: auto !important;\">\n" +
                "            <li class=\"ads-article grid\" style=\"height: auto !important;\"><script async=\"\" src=\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************\" crossorigin=\"anonymous\" data-checked-head=\"true\"></script>\n" +
                "<!-- CARL Ads 3 -->\n" +
                "<ins class=\"adsbygoogle adslot_3\" style=\"display: block; height: 280px;\" data-ad-client=\"ca-pub-****************\" data-ad-slot=\"6495961869\" data-ad-format=\"auto\" data-full-width-responsive=\"true\" data-adsbygoogle-status=\"done\" data-ad-status=\"filled\"><div id=\"aswift_2_host\" style=\"border: none; height: 280px; width: 351px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block; overflow: visible;\"><iframe id=\"aswift_2\" name=\"aswift_2\" browsingtopics=\"true\" style=\"left:0;position:absolute;top:0;border:0;width:351px;height:280px;\" sandbox=\"allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation\" width=\"351\" height=\"280\" frameborder=\"0\" marginwidth=\"0\" marginheight=\"0\" vspace=\"0\" hspace=\"0\" allowtransparency=\"true\" scrolling=\"no\" allow=\"attribution-reporting; run-ad-auction\" src=\"https://googleads.g.doubleclick.net/pagead/ads?client=ca-pub-****************&amp;output=html&amp;h=280&amp;slotname=6495961869&amp;adk=511129460&amp;adf=937278989&amp;pi=t.ma~as.6495961869&amp;w=351&amp;abgtt=6&amp;fwrn=4&amp;fwrnh=100&amp;lmt=1749109474&amp;rafmt=1&amp;format=351x280&amp;url=https%3A%2F%2Fcar.carslyst.com%2Fcar-tips%2F&amp;fwr=0&amp;fwrattr=true&amp;rpe=1&amp;resp_fmts=3&amp;wgl=1&amp;uach=***************************************************************************************************************************************************************************************************************.&amp;dt=1749109473956&amp;bpp=3&amp;bdt=520&amp;idt=198&amp;shv=r20250603&amp;mjsv=m202506020101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie=ID%3D87c7df928c4cccd8%3AT%3D1748916098%3ART%3D1749109328%3AS%3DALNI_Mb5syzIja-2zUMH7mR66TiHCCHNaA&amp;gpic=UID%3D0000111c6c6d1d76%3AT%3D1748916098%3ART%3D1749109328%3AS%3DALNI_Mbfdh80YdbdXJT-i20vh8TArBzscw&amp;eo_id_str=ID%3D83ce0e827dcaba66%3AT%3D1748916098%3ART%3D1749109328%3AS%3DAA-AfjYLb_vXTqLO55Q1-Op8duIC&amp;prev_fmts=0x0%2C1200x280&amp;nras=2&amp;correlator=5938903112465&amp;frm=20&amp;pv=1&amp;u_tz=480&amp;u_his=48&amp;u_h=1080&amp;u_w=1920&amp;u_ah=1040&amp;u_aw=1920&amp;u_cd=24&amp;u_sd=1&amp;dmc=8&amp;adx=403&amp;ady=574&amp;biw=1905&amp;bih=483&amp;scr_x=0&amp;scr_y=0&amp;eid=95353386%2C95362169&amp;oid=2&amp;pvsid=3255030753271617&amp;tmod=2040086066&amp;uas=0&amp;nvt=2&amp;ref=https%3A%2F%2Fcar.carslyst.com%2Fcar-tips%2F&amp;fc=1920&amp;brdim=-1917%2C11%2C-1917%2C11%2C1920%2C8%2C1914%2C1034%2C1920%2C483&amp;vis=1&amp;rsz=%7C%7CoEebr%7C&amp;abl=CS&amp;pfx=0&amp;fu=128&amp;bc=31&amp;bz=1&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=3&amp;uci=a!3&amp;btvi=1&amp;fsb=1&amp;dtd=202\" data-google-container-id=\"a!3\" tabindex=\"0\" title=\"Advertisement\" aria-label=\"Advertisement\" data-google-query-id=\"CNb9x8nk2Y0DFe1EwgUd230avw\" data-load-complete=\"true\"></iframe></div></ins>\n" +
                "<script>\n" +
                "     (adsbygoogle = window.adsbygoogle || []).push({});\n" +
                "</script></li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/full-coverage-car-insurance-build-your-perfect-auto-policy/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/full-coverage-car-insurance-build-your-perfect-auto-policy.jpg\" alt=\"Full Coverage Car Insurance: Build Your Perfect Auto Policy\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Car Insurance</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/full-coverage-car-insurance-build-your-perfect-auto-policy/\" class=\"article-page-link\">Full Coverage Car Insurance: Build Your Perfect Auto Policy</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/8-car-paint-tips-to-protect-your-vehicles-health/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/8-car-paint-tips-to-protect-your-vehicles-health.jpg\" alt=\"8 Car Paint Tips To Protect Your Vehicle's Health\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Auto Repair</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/8-car-paint-tips-to-protect-your-vehicles-health/\" class=\"article-page-link\">8 Car Paint Tips To Protect Your Vehicle's Health</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/master-tire-changes-8-skills-every-driver-should-know/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/master-tire-changes-8-skills-every-driver-should-know.jpg\" alt=\"Master Tire Changes: 8 Skills Every Driver Should Know\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Auto Repair</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/master-tire-changes-8-skills-every-driver-should-know/\" class=\"article-page-link\">Master Tire Changes: 8 Skills Every Driver Should Know</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"ads-article grid\" style=\"height: auto !important;\"><script async=\"\" src=\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************\" crossorigin=\"anonymous\" data-checked-head=\"true\"></script>\n" +
                "<!-- CARL Ads 3 -->\n" +
                "<ins class=\"adsbygoogle adslot_3\" style=\"display: block; height: 280px;\" data-ad-client=\"ca-pub-****************\" data-ad-slot=\"6495961869\" data-ad-format=\"auto\" data-full-width-responsive=\"true\" data-adsbygoogle-status=\"done\" data-ad-status=\"filled\"><div id=\"aswift_3_host\" style=\"border: none; height: 280px; width: 351px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block; overflow: visible;\"><iframe id=\"aswift_3\" name=\"aswift_3\" browsingtopics=\"true\" style=\"left:0;position:absolute;top:0;border:0;width:351px;height:280px;\" sandbox=\"allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation\" width=\"351\" height=\"280\" frameborder=\"0\" marginwidth=\"0\" marginheight=\"0\" vspace=\"0\" hspace=\"0\" allowtransparency=\"true\" scrolling=\"no\" allow=\"attribution-reporting; run-ad-auction\" src=\"https://googleads.g.doubleclick.net/pagead/ads?client=ca-pub-****************&amp;output=html&amp;h=280&amp;slotname=6495961869&amp;adk=511129460&amp;adf=4186410096&amp;pi=t.ma~as.6495961869&amp;w=351&amp;abgtt=6&amp;fwrn=4&amp;fwrnh=100&amp;lmt=1749109474&amp;rafmt=1&amp;format=351x280&amp;url=https%3A%2F%2Fcar.carslyst.com%2Fcar-tips%2F&amp;fwr=0&amp;fwrattr=true&amp;rpe=1&amp;resp_fmts=3&amp;wgl=1&amp;uach=***************************************************************************************************************************************************************************************************************.&amp;dt=1749109473960&amp;bpp=4&amp;bdt=523&amp;idt=209&amp;shv=r20250603&amp;mjsv=m202506020101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie=ID%3D87c7df928c4cccd8%3AT%3D1748916098%3ART%3D1749109328%3AS%3DALNI_Mb5syzIja-2zUMH7mR66TiHCCHNaA&amp;gpic=UID%3D0000111c6c6d1d76%3AT%3D1748916098%3ART%3D1749109328%3AS%3DALNI_Mbfdh80YdbdXJT-i20vh8TArBzscw&amp;eo_id_str=ID%3D83ce0e827dcaba66%3AT%3D1748916098%3ART%3D1749109328%3AS%3DAA-AfjYLb_vXTqLO55Q1-Op8duIC&amp;prev_fmts=0x0%2C1200x280%2C351x280&amp;nras=2&amp;correlator=5938903112465&amp;frm=20&amp;pv=1&amp;u_tz=480&amp;u_his=48&amp;u_h=1080&amp;u_w=1920&amp;u_ah=1040&amp;u_aw=1920&amp;u_cd=24&amp;u_sd=1&amp;dmc=8&amp;adx=777&amp;ady=906&amp;biw=1905&amp;bih=483&amp;scr_x=0&amp;scr_y=0&amp;eid=95353386%2C95362169&amp;oid=2&amp;pvsid=3255030753271617&amp;tmod=2040086066&amp;uas=0&amp;nvt=2&amp;ref=https%3A%2F%2Fcar.carslyst.com%2Fcar-tips%2F&amp;fc=1920&amp;brdim=-1917%2C11%2C-1917%2C11%2C1920%2C8%2C1914%2C1034%2C1920%2C483&amp;vis=1&amp;rsz=%7C%7CoEebr%7C&amp;abl=CS&amp;pfx=0&amp;fu=128&amp;bc=31&amp;bz=1&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=4&amp;uci=a!4&amp;btvi=2&amp;fsb=1&amp;dtd=213\" data-google-container-id=\"a!4\" tabindex=\"0\" title=\"Advertisement\" aria-label=\"Advertisement\" data-google-query-id=\"CMH0yMnk2Y0DFRlOwgUd-F4aaA\" data-load-complete=\"true\"></iframe></div></ins>\n" +
                "<script>\n" +
                "     (adsbygoogle = window.adsbygoogle || []).push({});\n" +
                "</script></li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/car-title-loan-and-its-risk-how-to-protect-your-vehicle/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/car-title-loan-and-its-risk-how-to-protect-your-vehicle.jpg\" alt=\"Car Title Loan And Its Risk: How To Protect Your Vehicle\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Car Loan</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/car-title-loan-and-its-risk-how-to-protect-your-vehicle/\" class=\"article-page-link\">Car Title Loan And Its Risk: How To Protect Your Vehicle</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/8-steps-to-find-affordable-car-insurance-that-actually-covers-you/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/8-steps-to-find-affordable-car-insurance-that-actually-covers-you.jpg\" alt=\"8 Steps To Find Affordable Car Insurance That Actually Covers You\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Car Insurance</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/8-steps-to-find-affordable-car-insurance-that-actually-covers-you/\" class=\"article-page-link\">8 Steps To Find Affordable Car Insurance That Actually Covers You</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/12-essential-car-insurance-types-every-driver-needs/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/12-essential-car-insurance-types-every-driver-needs.jpg\" alt=\"12 Essential Car Insurance Types Every Driver Needs\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Car Insurance</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/12-essential-car-insurance-types-every-driver-needs/\" class=\"article-page-link\">12 Essential Car Insurance Types Every Driver Needs</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"ads-article grid\" style=\"height: auto !important;\"><script async=\"\" src=\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************\" crossorigin=\"anonymous\" data-checked-head=\"true\"></script>\n" +
                "<!-- CARL Ads 3 -->\n" +
                "<ins class=\"adsbygoogle adslot_3\" style=\"display: block; height: 280px;\" data-ad-client=\"ca-pub-****************\" data-ad-slot=\"6495961869\" data-ad-format=\"auto\" data-full-width-responsive=\"true\" data-adsbygoogle-status=\"done\" data-ad-status=\"filled\"><div id=\"aswift_4_host\" style=\"border: none; height: 280px; width: 351px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block; overflow: visible;\"><iframe id=\"aswift_4\" name=\"aswift_4\" browsingtopics=\"true\" style=\"left:0;position:absolute;top:0;border:0;width:351px;height:280px;\" sandbox=\"allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation\" width=\"351\" height=\"280\" frameborder=\"0\" marginwidth=\"0\" marginheight=\"0\" vspace=\"0\" hspace=\"0\" allowtransparency=\"true\" scrolling=\"no\" allow=\"attribution-reporting; run-ad-auction\" src=\"https://googleads.g.doubleclick.net/pagead/ads?client=ca-pub-****************&amp;output=html&amp;h=280&amp;slotname=6495961869&amp;adk=511129460&amp;adf=567569171&amp;pi=t.ma~as.6495961869&amp;w=351&amp;abgtt=6&amp;fwrn=4&amp;fwrnh=100&amp;lmt=1749109474&amp;rafmt=1&amp;format=351x280&amp;url=https%3A%2F%2Fcar.carslyst.com%2Fcar-tips%2F&amp;fwr=0&amp;fwrattr=true&amp;rpe=1&amp;resp_fmts=3&amp;wgl=1&amp;uach=***************************************************************************************************************************************************************************************************************.&amp;dt=1749109473965&amp;bpp=2&amp;bdt=529&amp;idt=220&amp;shv=r20250603&amp;mjsv=m202506020101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie=ID%3D87c7df928c4cccd8%3AT%3D1748916098%3ART%3D1749109328%3AS%3DALNI_Mb5syzIja-2zUMH7mR66TiHCCHNaA&amp;gpic=UID%3D0000111c6c6d1d76%3AT%3D1748916098%3ART%3D1749109328%3AS%3DALNI_Mbfdh80YdbdXJT-i20vh8TArBzscw&amp;eo_id_str=ID%3D83ce0e827dcaba66%3AT%3D1748916098%3ART%3D1749109328%3AS%3DAA-AfjYLb_vXTqLO55Q1-Op8duIC&amp;prev_fmts=0x0%2C1200x280%2C351x280%2C351x280&amp;nras=2&amp;correlator=5938903112465&amp;frm=20&amp;pv=1&amp;u_tz=480&amp;u_his=48&amp;u_h=1080&amp;u_w=1920&amp;u_ah=1040&amp;u_aw=1920&amp;u_cd=24&amp;u_sd=1&amp;dmc=8&amp;adx=1152&amp;ady=1238&amp;biw=1905&amp;bih=483&amp;scr_x=0&amp;scr_y=0&amp;eid=95353386%2C95362169&amp;oid=2&amp;pvsid=3255030753271617&amp;tmod=2040086066&amp;uas=0&amp;nvt=2&amp;ref=https%3A%2F%2Fcar.carslyst.com%2Fcar-tips%2F&amp;fc=1920&amp;brdim=-1917%2C11%2C-1917%2C11%2C1920%2C8%2C1914%2C1034%2C1920%2C483&amp;vis=1&amp;rsz=%7C%7CoEebr%7C&amp;abl=CS&amp;pfx=0&amp;fu=128&amp;bc=31&amp;bz=1&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=5&amp;uci=a!5&amp;btvi=3&amp;fsb=1&amp;dtd=224\" data-google-container-id=\"a!5\" tabindex=\"0\" title=\"Advertisement\" aria-label=\"Advertisement\" data-google-query-id=\"COXzycnk2Y0DFUlEwgUdMJYsnw\" data-load-complete=\"true\"></iframe></div></ins>\n" +
                "<script>\n" +
                "     (adsbygoogle = window.adsbygoogle || []).push({});\n" +
                "</script></li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/8-critical-steps-to-take-after-a-car-accident/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/8-critical-steps-to-take-after-a-car-accident.jpg\" alt=\"8 Critical Steps To Take After A Car Accident In 2025\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Car Insurance</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/8-critical-steps-to-take-after-a-car-accident/\" class=\"article-page-link\">8 Critical Steps To Take After A Car Accident In 2025</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/8-electric-car-maintenance-tips-that-cut-your-costs/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/8-electric-car-maintenance-tips-that-cut-your-costs.jpg\" alt=\"8 Electric Car Maintenance Tips That Cut Your Costs\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Auto Repair</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/8-electric-car-maintenance-tips-that-cut-your-costs/\" class=\"article-page-link\">8 Electric Car Maintenance Tips That Cut Your Costs</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/car-loan-secrets-10-ways-to-get-the-best-deal/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/car-loan-secrets-10-ways-to-get-the-best-deal.jpg\" alt=\"Car Loan Secrets: 10 Ways To Get The Best Deal\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Car Loan</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/car-loan-secrets-10-ways-to-get-the-best-deal/\" class=\"article-page-link\">Car Loan Secrets: 10 Ways To Get The Best Deal</a></div>\n" +
                "            </li>\n" +
                "                        <li class=\"article-page-item grid\">\n" +
                "                <div class=\"article-page-img-wrap\">\n" +
                "                    <a href=\"/car-tips/9-most-reliable-used-cars-for-first-time-buyers/\" class=\"article-page-cover\"></a>\n" +
                "                    <img src=\"https://cdn.carslyst.com/blog/list/9-most-reliable-used-cars-for-first-time-buyers.jpg\" alt=\"9 Most Reliable Used Cars For First-Time Buyers\" class=\"article-page-img\">\n" +
                "                </div>\n" +
                "                <div class=\"article-page-tip\">Car Buying</div>\n" +
                "                <div class=\"article-page-b\"><a href=\"/car-tips/9-most-reliable-used-cars-for-first-time-buyers/\" class=\"article-page-link\">9 Most Reliable Used Cars For First-Time Buyers</a></div>\n" +
                "            </li>\n" +
                "                    <li class=\"ads-article grid\"><script async=\"\" src=\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************\" crossorigin=\"anonymous\"></script>\n" +
                "<!-- CARL Ads 3 -->\n" +
                "<ins class=\"adsbygoogle adslot_3\" style=\"display:block\" data-ad-client=\"ca-pub-****************\" data-ad-slot=\"6495961869\" data-ad-format=\"auto\" data-full-width-responsive=\"true\"></ins>\n" +
                "<script>\n" +
                "     (adsbygoogle = window.adsbygoogle || []).push({});\n" +
                "</script></li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/8-must-do-checks-when-buying-cars-private-sellers/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/8-must-do-checks-when-buying-cars-private-sellers.jpg\" alt=\"8 Must-Do Checks When Buying Cars From Private Sellers\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Buying</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/8-must-do-checks-when-buying-cars-private-sellers/\" class=\"article-page-link\">8 Must-Do Checks When Buying Cars From Private Sellers</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/10-best-cars-of-2025-expert-reviews-and-tests/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/10-best-cars-of-2025-expert-reviews-and-tests.jpg\" alt=\"10 Best Cars Of 2025: Expert Reviews And Tests\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Buying</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/10-best-cars-of-2025-expert-reviews-and-tests/\" class=\"article-page-link\">10 Best Cars Of 2025: Expert Reviews And Tests</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/complete-car-registration-guide-save-time-and-money/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/complete-car-registration-guide-save-time-and-money.jpg\" alt=\"Complete Car Registration Guide: Save Time And Money In 2025\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Buying</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/complete-car-registration-guide-save-time-and-money/\" class=\"article-page-link\">Complete Car Registration Guide: Save Time And Money In 2025</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/12-car-problems-that-every-driver-must-know/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/12-car-problems-that-every-driver-must-know.jpg\" alt=\"12 Car Problems That Every Driver Must Know\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Auto Repair</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/12-car-problems-that-every-driver-must-know/\" class=\"article-page-link\">12 Car Problems That Every Driver Must Know</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/what-does-auto-insurance-cover/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/what-does-auto-insurance-cover.jpg\" alt=\"What Does Auto Insurance Cover In 2025\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Insurance</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/what-does-auto-insurance-cover/\" class=\"article-page-link\">What Does Auto Insurance Cover In 2025</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/complete-guide-to-car-purchase-with-credit-cards/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/complete-guide-to-car-purchase-with-credit-cards.jpg\" alt=\"Complete Guide To Car Purchase With Credit Cards\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Buying</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/complete-guide-to-car-purchase-with-credit-cards/\" class=\"article-page-link\">Complete Guide To Car Purchase With Credit Cards</a></div>\n" +
                "                               </li><li class=\"ads-article grid\"><script async=\"\" src=\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************\" crossorigin=\"anonymous\"></script>\n" +
                "<!-- CARL Ads 3 -->\n" +
                "<ins class=\"adsbygoogle adslot_3\" style=\"display:block\" data-ad-client=\"ca-pub-****************\" data-ad-slot=\"6495961869\" data-ad-format=\"auto\" data-full-width-responsive=\"true\"></ins>\n" +
                "<script>\n" +
                "     (adsbygoogle = window.adsbygoogle || []).push({});\n" +
                "</script></li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/8-steps-to-file-your-car-insurance-claim-and-get-paid-fast/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/8-steps-to-file-your-car-insurance-claim-and-get-paid-fast.jpg\" alt=\"8 Steps To File Your Car Insurance Claim And Get Paid Fast\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Insurance</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/8-steps-to-file-your-car-insurance-claim-and-get-paid-fast/\" class=\"article-page-link\">8 Steps To File Your Car Insurance Claim And Get Paid Fast</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/8-steps-to-get-the-best-car-loan/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/8-steps-to-get-the-best-car-loan.jpg\" alt=\"8 Steps To Get The Best Car Loan\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Loan</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/8-steps-to-get-the-best-car-loan/\" class=\"article-page-link\">8 Steps To Get The Best Car Loan</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/subprime-auto-loans-a-guide-to-get-lower-interest-rates/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/subprime-auto-loans-a-guide-to-get-lower-interest-rates.jpg\" alt=\"Subprime Auto Loans: A Guide To Get Lower Interest Rates\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Loan</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/subprime-auto-loans-a-guide-to-get-lower-interest-rates/\" class=\"article-page-link\">Subprime Auto Loans: A Guide To Get Lower Interest Rates</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/how-to-get-the-best-car-loan-rate-a-complete-guide-for-buyers/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/how-to-get-the-best-car-loan-rate-a-complete-guide-for-buyers.jpg\" alt=\"How To Get The Best Car Loan Rate: A Complete Guide For Buyers\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Loan</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/how-to-get-the-best-car-loan-rate-a-complete-guide-for-buyers/\" class=\"article-page-link\">How To Get The Best Car Loan Rate: A Complete Guide For Buyers</a></div>\n" +
                "                               </li><li class=\"ads-article grid\"><script async=\"\" src=\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************\" crossorigin=\"anonymous\"></script>\n" +
                "<!-- CARL Ads 3 -->\n" +
                "<ins class=\"adsbygoogle adslot_3\" style=\"display:block\" data-ad-client=\"ca-pub-****************\" data-ad-slot=\"6495961869\" data-ad-format=\"auto\" data-full-width-responsive=\"true\"></ins>\n" +
                "<script>\n" +
                "     (adsbygoogle = window.adsbygoogle || []).push({});\n" +
                "</script></li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/expert-car-maintenance-guide-when-to-service-what/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/expert-car-maintenance-guide-when-to-service-what.jpg\" alt=\"Expert Car Maintenance Guide: When to Service What in 2025\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Auto Repair</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/expert-car-maintenance-guide-when-to-service-what/\" class=\"article-page-link\">Expert Car Maintenance Guide: When to Service What in 2025</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/when-to-buy-your-expert-guide-to-car-shopping/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/when-to-buy-your-expert-guide-to-car-shopping.jpg\" alt=\"When To Buy: Your Expert Guide To Car Shopping In 2025\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Buying</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/when-to-buy-your-expert-guide-to-car-shopping/\" class=\"article-page-link\">When To Buy: Your Expert Guide To Car Shopping In 2025</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/consumer-guide-top-10-reliable-cars-for-2025/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/consumer-guide-top-10-reliable-cars-for-2025.jpg\" alt=\"Consumer Guide: Top 10 Reliable Cars For 2025\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Buying</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/consumer-guide-top-10-reliable-cars-for-2025/\" class=\"article-page-link\">Consumer Guide: Top 10 Reliable Cars For 2025</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/12-essential-tips-for-buying-a-safe-used-car-in-2025/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/12-essential-tips-for-buying-a-safe-used-car-in-2025.jpg\" alt=\"12 Essential Tips For Buying A Safe Used Car In 2025\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Used Car</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/12-essential-tips-for-buying-a-safe-used-car-in-2025/\" class=\"article-page-link\">12 Essential Tips For Buying A Safe Used Car In 2025</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/how-can-i-find-a-nearby-tire-shop/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/how-can-i-find-a-nearby-tire-shop.jpg\" alt=\"How Can I Find A Nearby Tire Shop?\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Auto Repair</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/how-can-i-find-a-nearby-tire-shop/\" class=\"article-page-link\">How Can I Find A Nearby Tire Shop?</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/8-top-tips-for-inspecting-a-used-car-like-a-pro/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/8-top-tips-for-inspecting-a-used-car-like-a-pro.jpg\" alt=\"8 Top Tips For Inspecting A Used Car Like A Pro\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Used Car</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/8-top-tips-for-inspecting-a-used-car-like-a-pro/\" class=\"article-page-link\">8 Top Tips For Inspecting A Used Car Like A Pro</a></div>\n" +
                "                               </li><li class=\"ads-article grid\"><script async=\"\" src=\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************\" crossorigin=\"anonymous\"></script>\n" +
                "<!-- CARL Ads 3 -->\n" +
                "<ins class=\"adsbygoogle adslot_3\" style=\"display:block\" data-ad-client=\"ca-pub-****************\" data-ad-slot=\"6495961869\" data-ad-format=\"auto\" data-full-width-responsive=\"true\"></ins>\n" +
                "<script>\n" +
                "     (adsbygoogle = window.adsbygoogle || []).push({});\n" +
                "</script></li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/8-steps-to-buying-a-used-car/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/8-steps-to-buying-a-used-car.jpg\" alt=\"8 Steps To Buying A Used Car\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Used Car</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/8-steps-to-buying-a-used-car/\" class=\"article-page-link\">8 Steps To Buying A Used Car</a></div>\n" +
                "                               </li><li class=\"article-page-item grid\">\n" +
                "                                    <div class=\"article-page-img-wrap\">\n" +
                "                                        <a href=\"/car-tips/expert-guide-to-lower-your-car-insurance-rates/\" class=\"article-page-cover\"></a>\n" +
                "                                        <img src=\"https://cdn.carslyst.com/blog/list/expert-guide-to-lower-your-car-insurance-rates.jpg\" alt=\"Expert Guide To Lower Your Car Insurance Rates\" class=\"article-page-img\">\n" +
                "                                    </div>\n" +
                "                                    <div class=\"article-page-tip\">Car Insurance</div>\n" +
                "                                    <div class=\"article-page-b\"><a href=\"/car-tips/expert-guide-to-lower-your-car-insurance-rates/\" class=\"article-page-link\">Expert Guide To Lower Your Car Insurance Rates</a></div>\n" +
                "                               </li></ul>";
        Document tipsDoc = Jsoup.parse(s);
        Elements tips = tipsDoc.select("li.article-page-item.grid");
        ArrayList<TCarTip> tCarTips = new ArrayList<>();
        for (Element tip : tips) {
            String imgUrl = tip.select(".article-page-img").select("img").attr("src");
            Elements a = tip.select(".article-page-link").select("a");
            String href = a.attr("href");
            String title = a.text();
            //subCategory可以区分不同标签
            String subCategory = tip.select("div.article-page-tip").text();
            //进入文章详情
            String tipHtmlText = getHtmlText("https://car.carslyst.com"+href);
            if (tipHtmlText.isEmpty()){
                continue;
            }
            Document tipDoc = Jsoup.parse(tipHtmlText);
            Elements container = tipDoc.select("div.bottom div.container");
            String longDes = container.select("div.long-des").text();
            Elements content = container.select("div.article-section.long");
            ArrayList<DetailTag> articles = new ArrayList<>();
            Elements h3 = content.select("h3");
            //规律：1个h3,2个p，1个img
            for(int i = 0; i < h3.size(); i++){
                Element h = h3.get(i);
                DetailTag article = new DetailTag();
                article.setTag(h.tagName());
                article.setText(h.outerHtml());
                articles.add(article);
                Elements ps = content.select("p");
                if (ps.size() > 2*i){
                    DetailTag article1 = new DetailTag();
                    article1.setTag( ps.get(2*i).tagName());
                    article1.setText(ps.get(2*i).outerHtml());
                    articles.add(article1);
                }if (ps.size() > 2*i+1){
                    DetailTag article2 = new DetailTag();
                    article2.setTag( ps.get(2*i+1).tagName());
                    article2.setText(ps.get(2*i+1).outerHtml());
                    articles.add(article2);
                }
                Elements imgs = content.select("img.long-img");
                if(imgs.size() > i){
                    DetailTag article3 = new DetailTag();
                    Element img =imgs.get(i);
                    imgUrl = img.attr("data-src");
                    imgUrl=uploadImg(imgUrl);
                    article3.setTag(img.tagName());
                    article3.setText("<img src=\""+imgUrl+"\">");
                    articles.add(article3);
                }
            }
            TCarTip tCarTip = new TCarTip();
            imgUrl=uploadImg(imgUrl);
            tCarTip.setCover(imgUrl);
            tCarTip.setTitle(title);
            tCarTip.setSubCategory(subCategory);
            tCarTip.setDescription(longDes);
            tCarTip.setDetail(JSON.toJSONString(articles));
            tCarTips.add(tCarTip);
        }
        int i = carTipMapper.insertBatch(tCarTips);
    }

    public void pullMajorCityLocation(){
        String url = "https://car.carslyst.com/car-dealers/";
        String majorCityHtmlText = getHtmlText(url);
        log.info("majorCityHtmlText:{}", majorCityHtmlText);
        Document majorCityDoc = Jsoup.parse(majorCityHtmlText);
        // 获取所有state列表
        Element ul = majorCityDoc.select("ul.general-list.state-list").get(1);
        Elements a = ul.select("a");
        CountDownLatch countDownLatch = new CountDownLatch(a.size());
        for (Element element : a) {
            String href = "https://car.carslyst.com" + element.attr("href");
            String majorCityName = element.select("span").text();
            log.info("majorCity名称:{}", majorCityName);
            String majorCityHtmlText1 = getHtmlText(href);
            if (majorCityHtmlText1.isEmpty()){
                continue;
            }
            Document majorCityDoc1 = Jsoup.parse(majorCityHtmlText1);
            Elements select = majorCityDoc1.select("a.page-nor");
            String lastPage="1";
            if (select.isEmpty()){
                lastPage="1";
            }else{
                lastPage = select.last().text();
            }
            int pageNum = Integer.parseInt(lastPage);
            executorService.submit(() -> {
                for (int i = 1; i <= pageNum; i++) {
                    int finalI = i;
                    try {
                        getMajorCityLocationList(href, majorCityName, finalI);
                    } catch (Exception e) {
                        log.info("获取majorCityLocation列表信息失败:{}", e.getMessage());
                    }
                }
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
    private void getMajorCityLocationList(String url, String majorCityName, int pageNum) {
        String majorCityHtmlText = getHtmlText(url + "?page=" + pageNum);
        if (majorCityHtmlText.isEmpty()){
            return;
        }
        Document majorCityDoc = Jsoup.parse(majorCityHtmlText);
        Elements elements = majorCityDoc.select("li.office-location-item");
        ArrayList<TMajorCityLocation> tMajorCityLocations = new ArrayList<>();
        for (Element element : elements) {
            String a = element.select("a.office-location-link").first().attr("href");
            String imgUrl = element.select(".location-pic").select("img").attr("data-src");
            imgUrl=uploadImg(imgUrl);
            String locationHtmlText = getHtmlText("https://car.carslyst.com" + a);
            if (locationHtmlText.isEmpty()){
                continue;
            }
            TMajorCityLocation tMajorCityLocation = new TMajorCityLocation();
            tMajorCityLocation.setMajorCity(majorCityName);

            tMajorCityLocation.setImg(imgUrl);
            extractLocation(locationHtmlText, tMajorCityLocation);
            tMajorCityLocation.setCategory("Car Dealers");
            tMajorCityLocation.setSubCategory("Major City");
            tMajorCityLocations.add(tMajorCityLocation);

        }
        int num=majorCityLocationMapper.insertBatch(tMajorCityLocations);

    }
    private void extractLocation(String locationHtmlText, TMajorCityLocation majorCityLocation) {
        Document locationDoc = Jsoup.parse(locationHtmlText);
        String map = locationDoc.select("div.detail-map").outerHtml();
        majorCityLocation.setMap(map);
        String title = locationDoc.select("div.container").select("h1").first().text();

//        log.info("title:{}", title);
        //先获得所有li
        Elements li = locationDoc.select(".container div.general-wrap").first().select("li.detail-info-item");
        for(Element element : li){
            String attr = element.select("use").attr("xlink:href");
            switch (attr){
                case "#icon-user":
                    majorCityLocation.setPerson(element.select("span").text());
                    break;
                case "#icon-location":
                    majorCityLocation.setLocation(element.select("span").text());
                    break;
                case "#icon-tel":
                    majorCityLocation.setPhone(element.select("span").text());
                    break;
                case "#icon-web":
                    majorCityLocation.setNetAddress(element.select("span").text());
                    break;
                case "#icon-hours":
                    majorCityLocation.setDate(element.select("span.hours-status-list").toString());
            }
        }
        Elements generalWrapelEments = locationDoc.select(".container div.general-wrap");
        String supportedMakesHtml="";
        if (generalWrapelEments.size() >= 2){
            supportedMakesHtml = generalWrapelEments.get(1).select(".service-list").toString();
        }
        String servicesOfferedHtml="";
        if (generalWrapelEments.size() >= 3){
            servicesOfferedHtml = generalWrapelEments.get(2).select(".service-list").toString();
        }
        String amenity = locationDoc.select("ul.amenities-list").toString();
        String description = locationDoc.select("p.description-text.about").toString();
        majorCityLocation.setAmenity(amenity);
        majorCityLocation.setDescription(description);
        majorCityLocation.setTitle(title);
        majorCityLocation.setSupportedMakes(supportedMakesHtml);
        majorCityLocation.setServicesOffered(servicesOfferedHtml);
    }
}
