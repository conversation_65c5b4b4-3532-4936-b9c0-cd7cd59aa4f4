package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.dto.linestore.LineStoreDetailReq;
import com.apk.website.dto.linestore.LineStoreListReq;
import com.apk.website.dto.linestore.LineStoreSearchReq;
import com.apk.website.entity.TEmojiData;
import com.apk.website.entity.TStickerData;
import com.apk.website.mapper.TEmojiInfoDataMapper;
import com.apk.website.mapper.TStickerInfoDataMapper;
import com.apk.website.vo.LineStoreEmojiDetailVo;
import com.apk.website.vo.LineStoreStickerDetailVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor

public class LineStoreService {

    private final TStickerInfoDataMapper stickerMapper;
    private final TEmojiInfoDataMapper emojiMapper;

    public <T> PageData<T> getStickerList(LineStoreListReq req) {
        if (req.getType() == 1) {
            LambdaQueryWrapper<TStickerData> wrapper = new LambdaQueryWrapper<>(TStickerData.class);
            wrapper.eq(TStickerData::getType, "0");
            wrapper.eq(req.getCategoryId() != null,TStickerData::getCategoryId, req.getCategoryId());
            wrapper.eq(req.getAuthorId() != null,TStickerData::getAuthorId, req.getAuthorId());

            IPage<TStickerData> page = new Page<>(req.getPageNum(), req.getPageSize());

            IPage<TStickerData> iPage = stickerMapper.selectPage(page, wrapper);

            return (PageData<T>) PageUtils.coverPage(iPage);
        }else if (req.getType() == 2){
            LambdaQueryWrapper<TEmojiData> wrapper = new LambdaQueryWrapper<>(TEmojiData.class);
            wrapper.eq(TEmojiData::getType, "0");
            wrapper.eq(req.getCategoryId() != null,TEmojiData::getCategoryId, req.getCategoryId());
            wrapper.eq(req.getAuthorId() != null,TEmojiData::getAuthorId, req.getAuthorId());

            IPage<TEmojiData> page = new Page<>(req.getPageNum(), req.getPageSize());

            IPage<TEmojiData> iPage = emojiMapper.selectPage(page, wrapper);

            return (PageData<T>) PageUtils.coverPage(iPage);
        }else {
            return null;
        }

    }
    public <T> List<T> search(LineStoreSearchReq req) {
        if (req.getType() == 1) {
            LambdaQueryWrapper<TStickerData> wrapper = new LambdaQueryWrapper<>(TStickerData.class);
            wrapper.like(TStickerData::getTitle, req.getKeywords());
            return (List<T>) stickerMapper.selectList(wrapper);
        }else if (req.getType() == 2){
            LambdaQueryWrapper<TEmojiData> wrapper = new LambdaQueryWrapper<>(TEmojiData.class);
            wrapper.like(TEmojiData::getTitle, req.getKeywords());
            return (List<T>) emojiMapper.selectList(wrapper);
        }else {
            return null;
        }

    }

    public <T> T detail(LineStoreDetailReq req) {
        if (req.getType() == 1) {
            LineStoreStickerDetailVo resp = new LineStoreStickerDetailVo();
            TStickerData result = stickerMapper.selectById(req.getId());

            resp.setId(Integer.parseInt(req.getId()));
            resp.setTitle(result.getTitle());
            resp.setAuthorId(result.getAuthorId());
            resp.setAuthorName(result.getAuthorName());
            resp.setCategoryId(result.getCategoryId());
            resp.setCategoryName(result.getCategoryName());
            resp.setCover(result.getCover());
            resp.setDescription(result.getDescription());
            resp.setPrice(result.getPrice());
            resp.setStickersList(result.getStickersList());
            resp.setType(result.getType());
            resp.setCreateTime(result.getCreateTime());
            resp.setUpdateTime(result.getUpdateTime());

            LambdaQueryWrapper<TStickerData> wrapper = new LambdaQueryWrapper<>(TStickerData.class);
            wrapper.eq(TStickerData::getAuthorId, result.getAuthorId());
            resp.setOtherStickerWorks(stickerMapper.selectList(wrapper));
            wrapper.clear();
            wrapper.eq(TStickerData::getCategoryId, result.getCategoryId());
            wrapper.last("ORDER BY RAND() LIMIT 18"); // 添加随机排序
            resp.setRelatedStickerWorks(stickerMapper.selectList(wrapper));
            wrapper.clear();
            // 上一条：比当前ID小的最大ID
            wrapper.lt(TStickerData::getId, result.getId())
                    .orderByDesc(TStickerData::getId)
                    .last("LIMIT 1");
            TStickerData prevEntity = stickerMapper.selectOne(wrapper);
            resp.setPrevId(prevEntity != null ? prevEntity.getId() : null);

            // 下一条：比当前ID大的最小ID
            LambdaQueryWrapper<TStickerData> nextWrapper = new LambdaQueryWrapper<>();
            nextWrapper.gt(TStickerData::getId, result.getId())
                    .orderByAsc(TStickerData::getId)
                    .last("LIMIT 1");
            TStickerData nextEntity = stickerMapper.selectOne(nextWrapper);
            resp.setNextId(nextEntity != null ? nextEntity.getId() : null);

            return (T) resp;
        }else if (req.getType() == 2){
            LineStoreEmojiDetailVo resp = new LineStoreEmojiDetailVo();
            TEmojiData result = emojiMapper.selectById(req.getId());

            resp.setId(req.getId());
            resp.setAuthorId(result.getAuthorId());
            resp.setTitle(result.getTitle());
            resp.setAuthorName(result.getAuthorName());
            resp.setCategoryId(result.getCategoryId());
            resp.setCategoryName(result.getCategoryName());
            resp.setCover(result.getCover());
            resp.setDescription(result.getDescription());
            resp.setPrice(result.getPrice());
            resp.setStickersList(result.getStickersList());
            resp.setType(result.getType());
            resp.setCreateTime(result.getCreateTime());
            resp.setUpdateTime(result.getUpdateTime());

            LambdaQueryWrapper<TEmojiData> wrapper = new LambdaQueryWrapper<>(TEmojiData.class);
            wrapper.eq(TEmojiData::getAuthorId, result.getAuthorId());
            resp.setOtherEmojiWorks(emojiMapper.selectList(wrapper));
            wrapper.clear();
            wrapper.eq(TEmojiData::getCategoryId, result.getCategoryId());
            wrapper.last("ORDER BY RAND() LIMIT 18"); // 添加随机排序
            resp.setRelatedEmojiWorks(emojiMapper.selectList(wrapper));
            wrapper.clear();
            // 上一条：比当前ID小的最大ID
            wrapper.lt(TEmojiData::getId, result.getId())
                    .orderByDesc(TEmojiData::getId)
                    .last("LIMIT 1");
            TEmojiData prevEntity = emojiMapper.selectOne(wrapper);
            resp.setPrevId(prevEntity != null ? prevEntity.getId() : null);

            // 下一条：比当前ID大的最小ID
            LambdaQueryWrapper<TEmojiData> nextWrapper = new LambdaQueryWrapper<>();
            nextWrapper.gt(TEmojiData::getId, result.getId())
                    .orderByAsc(TEmojiData::getId)
                    .last("LIMIT 1");
            TEmojiData nextEntity = emojiMapper.selectOne(nextWrapper);
            resp.setNextId(nextEntity != null ? nextEntity.getId() : null);


            return (T) resp;
        }else {
            return null;
        }

    }


}
