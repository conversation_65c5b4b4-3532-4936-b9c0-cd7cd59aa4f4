package com.apk.website.vo;

import com.apk.website.entity.TEmojiData;
import com.apk.website.entity.TStickerData;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class LineStoreEmojiDetailVo {
    private String id;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 分类名
     */
    private String categoryName;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面图
     */
    private String cover;

    /**
     * 作者ID
     */
    private Integer authorId;

    /**
     * 作者名
     */
    private String authorName;

    /**
     * 介绍
     */
    private String description;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 贴图列表
     */
    private String stickersList;

    /**
     * 类型， 0 官方 1个人
     */
    private String type;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    private String prevId;
    private String nextId;

    // 相同作者的其他作品
    private List<TEmojiData> otherEmojiWorks;


    //相关作品
    private List<TEmojiData> relatedEmojiWorks;
}
