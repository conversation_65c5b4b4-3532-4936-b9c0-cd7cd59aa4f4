package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 表情包数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@TableName("t_emoji_data")
public class TEmojiData implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 分类名
     */
    private String categoryName;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面图
     */
    private String cover;

    /**
     * 作者ID
     */
    private Integer authorId;

    /**
     * 作者名
     */
    private String authorName;

    /**
     * 介绍
     */
    private String description;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 贴图列表
     */
    private String stickersList;

    /**
     * 类型， 0 官方 1个人
     */
    private String type;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
}

